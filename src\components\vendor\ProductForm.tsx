import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Package } from 'lucide-react';
import type { Product } from '@/types/database';

// 表單驗證 Schema
const productFormSchema = z.object({
  name: z.string().min(1, '請輸入商品名稱').max(100, '商品名稱不能超過100字'),
  description: z.string().optional(),
  price: z.number().min(0.01, '價格必須大於0').max(999999, '價格不能超過999999'),
  stock: z.number().int().min(0, '庫存不能為負數').max(999999, '庫存不能超過999999'),
  unit: z.enum(['KG', 'G', 'PIECE', 'BOX', 'JIN'], {
    required_error: '請選擇計價單位'
  }),
  category: z.string().min(1, '請選擇商品分類'),
  status: z.enum(['ACTIVE', 'INACTIVE'], {
    required_error: '請選擇商品狀態'
  }),
  image_url: z.string().optional(),
});

type ProductFormData = z.infer<typeof productFormSchema>;

interface ProductFormProps {
  product?: Product;
  onSubmit: (data: ProductFormData) => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

/**
 * 商品表單組件
 * 用於新增和編輯商品
 */
const ProductForm: React.FC<ProductFormProps> = ({
  product,
  onSubmit,
  isLoading = false,
  mode
}) => {
  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price || 0,
      stock: product?.stock || 0,
      unit: product?.unit || 'KG',
      category: product?.category || '',
      status: product?.status || 'ACTIVE',
      image_url: product?.image_url || '',
    },
  });

  const handleSubmit = (data: ProductFormData) => {
    onSubmit(data);
  };

  const categories = [
    '葉菜類',
    '根莖類', 
    '果菜類',
    '菇類',
    '辛香料',
    '其他'
  ];

  const units = [
    { value: 'KG', label: '公斤 (KG)' },
    { value: 'G', label: '公克 (G)' },
    { value: 'PIECE', label: '個 (PIECE)' },
    { value: 'BOX', label: '箱 (BOX)' },
    { value: 'JIN', label: '斤 (JIN)' },
  ];

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>{mode === 'create' ? '新增商品' : '編輯商品'}</span>
          </CardTitle>
          <CardDescription>
            {mode === 'create' 
              ? '填寫商品資訊來新增商品到您的商店'
              : '修改商品資訊'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* 基本資訊 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">基本資訊</h3>
                
                {/* 商品名稱 */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>商品名稱 *</FormLabel>
                      <FormControl>
                        <Input placeholder="請輸入商品名稱" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 商品描述 */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>商品描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="請輸入商品描述（選填）"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        詳細的商品描述有助於客戶了解商品特色
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 商品分類 */}
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>商品分類 *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="請選擇商品分類" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 價格與庫存 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">價格與庫存</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 價格 */}
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>價格 *</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>單位價格（新台幣）</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 計價單位 */}
                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>計價單位 *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="請選擇單位" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {units.map((unit) => (
                              <SelectItem key={unit.value} value={unit.value}>
                                {unit.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 庫存數量 */}
                <FormField
                  control={form.control}
                  name="stock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>庫存數量 *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormDescription>
                        當前可販售的庫存數量
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 商品狀態 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">商品狀態</h3>
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>上架狀態 *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="請選擇狀態" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ACTIVE">上架中</SelectItem>
                          <SelectItem value="INACTIVE">已下架</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        只有上架中的商品才會在前台顯示
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 商品圖片 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">商品圖片</h3>
                
                <FormField
                  control={form.control}
                  name="image_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>圖片網址</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="請輸入圖片網址（選填）"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        暫時使用圖片網址，未來將支援圖片上傳功能
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 圖片預覽 */}
                {form.watch('image_url') && (
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">圖片預覽：</p>
                    <div className="w-32 h-32 border border-gray-200 rounded-lg overflow-hidden">
                      <img
                        src={form.watch('image_url')}
                        alt="商品預覽"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* 提交按鈕 */}
              <div className="flex justify-end space-x-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => window.history.back()}>
                  取消
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {mode === 'create' ? '新增商品' : '更新商品'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductForm;
