
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  unit: string;
  image_url?: string;
  vendor_name?: string;
  vendor_id?: string;
}

interface CartState {
  items: CartItem[];
  isOpen: boolean;
  totalAmount: number;
  totalItems: number;
  addItem: (item: CartItem) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  setIsOpen: (isOpen: boolean) => void;
}

const calculateTotals = (items: CartItem[]) => {
  const totalAmount = items.reduce((total, item) => total + (item.price * item.quantity), 0);
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  return { totalAmount, totalItems };
};

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      totalAmount: 0,
      totalItems: 0,
      addItem: (item) => set((state) => {
        const existingItem = state.items.find(i => i.id === item.id);

        let newItems;
        if (existingItem) {
          newItems = state.items.map(i =>
            i.id === item.id
              ? { ...i, quantity: i.quantity + item.quantity }
              : i
          );
        } else {
          newItems = [...state.items, item];
        }

        const { totalAmount, totalItems } = calculateTotals(newItems);
        return { items: newItems, totalAmount, totalItems };
      }),
      removeItem: (productId) => set((state) => {
        const newItems = state.items.filter(item => item.id !== productId);
        const { totalAmount, totalItems } = calculateTotals(newItems);
        return { items: newItems, totalAmount, totalItems };
      }),
      updateQuantity: (productId, quantity) => set((state) => {
        let newItems;
        if (quantity <= 0) {
          newItems = state.items.filter(item => item.id !== productId);
        } else {
          newItems = state.items.map(item =>
            item.id === productId ? { ...item, quantity } : item
          );
        }

        const { totalAmount, totalItems } = calculateTotals(newItems);
        return { items: newItems, totalAmount, totalItems };
      }),
      clearCart: () => set({ items: [], totalAmount: 0, totalItems: 0 }),
      setIsOpen: (isOpen) => set({ isOpen }),
    }),
    {
      name: 'cart-store',
      partialize: (state) => ({ items: state.items }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          const { totalAmount, totalItems } = calculateTotals(state.items);
          state.totalAmount = totalAmount;
          state.totalItems = totalItems;
        }
      },
    }
  )
);
