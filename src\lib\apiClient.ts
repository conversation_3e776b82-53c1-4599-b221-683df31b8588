
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const handleApiError = (error: any, customMessage?: string) => {
  console.error('API Error:', error);
  
  const message = customMessage || error.message || '發生未知錯誤';
  toast.error(message);
  
  throw new ApiError(message, error.status, error.code);
};

// Typed API service functions
export const apiService = {
  // Products
  async getProducts(filters?: any) {
    try {
      let query = supabase
        .from('products')
        .select(`
          *,
          vendor:vendors(*)
        `)
        .eq('status', 'ACTIVE');

      if (filters?.vendor_id) {
        query = query.eq('vendor_id', filters.vendor_id);
      }
      if (filters?.category) {
        query = query.eq('category', filters.category);
      }
      if (filters?.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    } catch (error) {
      handleApiError(error, '獲取產品列表失敗');
    }
  },

  async getProductById(id: string) {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          vendor:vendors(*)
        `)
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      handleApiError(error, '獲取產品詳情失敗');
    }
  },

  // Categories
  async getCategories() {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('sort_order', { ascending: true });
      
      if (error) throw error;
      return data;
    } catch (error) {
      handleApiError(error, '獲取分類列表失敗');
    }
  },

  // Vendors
  async getVendors(filters?: any) {
    try {
      let query = supabase
        .from('vendors')
        .select('*')
        .eq('status', 'ACTIVE');

      const { data, error } = await query.order('name', { ascending: true });
      
      if (error) throw error;
      return data;
    } catch (error) {
      handleApiError(error, '獲取供應商列表失敗');
    }
  },

  // Orders
  async createOrder(orderData: any) {
    try {
      const total_amount = orderData.items.reduce(
        (sum: number, item: any) => sum + (item.quantity * item.unit_price), 0
      );

      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_name: orderData.customer_name,
          customer_phone: orderData.customer_phone,
          license_plate: orderData.license_plate,
          notes: orderData.notes,
          total_amount
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = orderData.items.map((item: any) => ({
        order_id: order.id,
        ...item,
        subtotal: item.quantity * item.unit_price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Update product stock
      for (const item of orderData.items) {
        const { error: stockError } = await supabase.rpc('decrement_stock', {
          product_id: item.product_id,
          quantity: item.quantity
        });

        if (stockError) {
          console.error('Stock update error:', stockError);
        }
      }

      toast.success('訂單創建成功！');
      return order;
    } catch (error) {
      handleApiError(error, '創建訂單失敗');
    }
  },
};
