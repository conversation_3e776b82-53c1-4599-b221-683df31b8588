
import { useCartStore } from '@/stores/cartStore';
import { toast } from 'sonner';

export interface CartItem {
  product_id: string;
  vendor_id: string;
  product_name: string;
  unit_price: number;
  quantity: number;
  unit: string;
  image_url?: string;
  vendor_name?: string;
}

export const useCart = () => {
  const {
    items,
    isOpen,
    addItem: addToStore,
    removeItem,
    updateQuantity,
    clearCart: clearStore,
    toggleCart,
    setCartOpen,
    getTotalAmount,
    getTotalItems,
  } = useCartStore();

  const addItem = (item: CartItem) => {
    addToStore(item);
    toast.success(`${item.product_name} 已加入購物車`);
  };

  const removeItemWithNotification = (productId: string) => {
    removeItem(productId);
    toast.success('商品已從購物車移除');
  };

  const updateQuantityWithValidation = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItemWithNotification(productId);
      return;
    }
    updateQuantity(productId, quantity);
  };

  const clearCart = () => {
    clearStore();
    toast.success('購物車已清空');
  };

  return {
    items,
    isOpen,
    addItem,
    updateQuantity: updateQuantityWithValidation,
    removeItem: removeItemWithNotification,
    clearCart,
    toggleCart,
    setCartOpen,
    getTotalAmount,
    getTotalItems,
    isEmpty: items.length === 0
  };
};
