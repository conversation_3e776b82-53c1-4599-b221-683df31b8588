import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { 
  VendorProductFilter, 
  VendorOrderFilter,
  VendorDashboardStats 
} from '@/types/database';

interface VendorState {
  // 商品管理相關狀態
  productFilters: VendorProductFilter;
  setProductFilter: (key: keyof VendorProductFilter, value: any) => void;
  clearProductFilters: () => void;

  // 訂單管理相關狀態
  orderFilters: VendorOrderFilter;
  setOrderFilter: (key: keyof VendorOrderFilter, value: any) => void;
  clearOrderFilters: () => void;

  // 儀表板相關狀態
  dashboardStats: VendorDashboardStats | null;
  setDashboardStats: (stats: VendorDashboardStats) => void;

  // UI 狀態
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  // 選中的項目
  selectedProducts: string[];
  selectedOrders: string[];
  setSelectedProducts: (ids: string[]) => void;
  setSelectedOrders: (ids: string[]) => void;
  toggleProductSelection: (id: string) => void;
  toggleOrderSelection: (id: string) => void;
  clearSelections: () => void;

  // 批量操作狀態
  bulkActionLoading: boolean;
  setBulkActionLoading: (loading: boolean) => void;
}

/**
 * 廠商狀態管理 Store
 * 管理廠商後台的各種狀態和操作
 */
export const useVendorStore = create<VendorState>()(
  persist(
    (set, get) => ({
      // 商品篩選器初始狀態
      productFilters: {
        status: undefined,
        category: undefined,
        search: undefined,
        sortBy: 'created_at',
        sortOrder: 'desc',
        page: 1,
        limit: 20,
      },

      setProductFilter: (key, value) => set((state) => ({
        productFilters: { 
          ...state.productFilters, 
          [key]: value,
          // 當改變篩選條件時，重置頁碼
          ...(key !== 'page' && { page: 1 })
        }
      })),

      clearProductFilters: () => set({
        productFilters: {
          status: undefined,
          category: undefined,
          search: undefined,
          sortBy: 'created_at',
          sortOrder: 'desc',
          page: 1,
          limit: 20,
        }
      }),

      // 訂單篩選器初始狀態
      orderFilters: {
        status: undefined,
        dateFrom: undefined,
        dateTo: undefined,
        search: undefined,
        page: 1,
        limit: 20,
      },

      setOrderFilter: (key, value) => set((state) => ({
        orderFilters: { 
          ...state.orderFilters, 
          [key]: value,
          // 當改變篩選條件時，重置頁碼
          ...(key !== 'page' && { page: 1 })
        }
      })),

      clearOrderFilters: () => set({
        orderFilters: {
          status: undefined,
          dateFrom: undefined,
          dateTo: undefined,
          search: undefined,
          page: 1,
          limit: 20,
        }
      }),

      // 儀表板統計
      dashboardStats: null,
      setDashboardStats: (stats) => set({ dashboardStats: stats }),

      // UI 狀態
      sidebarOpen: false,
      setSidebarOpen: (open) => set({ sidebarOpen: open }),

      // 選中項目
      selectedProducts: [],
      selectedOrders: [],
      
      setSelectedProducts: (ids) => set({ selectedProducts: ids }),
      setSelectedOrders: (ids) => set({ selectedOrders: ids }),

      toggleProductSelection: (id) => set((state) => ({
        selectedProducts: state.selectedProducts.includes(id)
          ? state.selectedProducts.filter(pid => pid !== id)
          : [...state.selectedProducts, id]
      })),

      toggleOrderSelection: (id) => set((state) => ({
        selectedOrders: state.selectedOrders.includes(id)
          ? state.selectedOrders.filter(oid => oid !== id)
          : [...state.selectedOrders, id]
      })),

      clearSelections: () => set({ 
        selectedProducts: [], 
        selectedOrders: [] 
      }),

      // 批量操作狀態
      bulkActionLoading: false,
      setBulkActionLoading: (loading) => set({ bulkActionLoading: loading }),
    }),
    {
      name: 'vendor-store',
      // 只持久化篩選器和 UI 狀態，不持久化統計數據和選中項目
      partialize: (state) => ({
        productFilters: state.productFilters,
        orderFilters: state.orderFilters,
        sidebarOpen: state.sidebarOpen,
      }),
    }
  )
);

/**
 * 廠商商品管理相關的 Hook
 */
export const useVendorProductStore = () => {
  const {
    productFilters,
    setProductFilter,
    clearProductFilters,
    selectedProducts,
    setSelectedProducts,
    toggleProductSelection,
    bulkActionLoading,
    setBulkActionLoading,
  } = useVendorStore();

  return {
    filters: productFilters,
    setFilter: setProductFilter,
    clearFilters: clearProductFilters,
    selectedProducts,
    setSelectedProducts,
    toggleSelection: toggleProductSelection,
    bulkActionLoading,
    setBulkActionLoading,
  };
};

/**
 * 廠商訂單管理相關的 Hook
 */
export const useVendorOrderStore = () => {
  const {
    orderFilters,
    setOrderFilter,
    clearOrderFilters,
    selectedOrders,
    setSelectedOrders,
    toggleOrderSelection,
    bulkActionLoading,
    setBulkActionLoading,
  } = useVendorStore();

  return {
    filters: orderFilters,
    setFilter: setOrderFilter,
    clearFilters: clearOrderFilters,
    selectedOrders,
    setSelectedOrders,
    toggleSelection: toggleOrderSelection,
    bulkActionLoading,
    setBulkActionLoading,
  };
};
