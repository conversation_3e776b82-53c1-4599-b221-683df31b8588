
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, Eye } from 'lucide-react';
import AddToCartButton from './AddToCartButton';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  unit: string;
  image: string;
  vendor: string;
  location: string;
  rating: number;
  reviewCount: number;
  isOrganic?: boolean;
  inStock: boolean;
  stock: number;
  minOrder?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  name,
  price,
  unit,
  image,
  vendor,
  location,
  rating,
  reviewCount,
  isOrganic = false,
  inStock,
  stock,
  minOrder
}) => {
  return (
    <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
      <div className="relative">
        <Link to={`/products/${id}`}>
          <img
            src={image}
            alt={name}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300 cursor-pointer"
          />
        </Link>
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {isOrganic && (
            <Badge className="bg-market-green-500 text-white">
              🌱 有機
            </Badge>
          )}
          {!inStock && (
            <Badge variant="destructive">
              缺貨
            </Badge>
          )}
        </div>
        <div className="absolute top-3 right-3">
          <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-sm font-medium">
            💰 {price}/{unit}
          </div>
        </div>
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Product name */}
          <Link to={`/products/${id}`}>
            <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 group-hover:text-market-green-600 transition-colors cursor-pointer">
              {name}
            </h3>
          </Link>

          {/* Vendor info */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <span className="font-medium">{vendor}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MapPin className="w-3 h-3" />
              <span>{location}</span>
            </div>
          </div>

          {/* Rating */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium">{rating}</span>
            </div>
            <span className="text-sm text-gray-500">({reviewCount} 評價)</span>
          </div>

          {/* Price and order info */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-market-green-600">
                ${price}
              </div>
              <div className="text-sm text-gray-500">
                每{unit} {minOrder && `• 最少 ${minOrder}${unit}`}
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-2 pt-2">
            <Link to={`/products/${id}`} className="flex-1">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Eye className="w-4 h-4 mr-1" />
                查看詳情
              </Button>
            </Link>
            <div className="flex-1">
              <AddToCartButton
                product={{
                  id,
                  name,
                  price,
                  unit,
                  image_url: image,
                  vendor_name: vendor,
                  stock
                }}
                variant="compact"
                className="w-full"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
