
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { Vendor } from '@/types/database';

export const useVendors = () => {
  return useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('status', 'ACTIVE')
        .order('name');

      if (error) {
        console.error('Error fetching vendors:', error);
        throw error;
      }

      return data as Vendor[];
    }
  });
};

export const useVendorProfile = (vendorId?: string) => {
  return useQuery({
    queryKey: ['vendor', vendorId],
    queryFn: async () => {
      if (!vendorId) return null;

      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('id', vendorId)
        .single();

      if (error) throw error;
      return data as Vendor;
    },
    enabled: !!vendorId
  });
};

export const useVendorMutations = () => {
  const queryClient = useQueryClient();

  const updateVendorMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Vendor> }) => {
      const { data, error } = await supabase
        .from('vendors')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success('供應商資料更新成功！');
      queryClient.invalidateQueries({ queryKey: ['vendors'] });
      queryClient.invalidateQueries({ queryKey: ['vendor'] });
    },
    onError: (error: any) => {
      console.error('Update vendor error:', error);
      toast.error(error.message || '供應商資料更新失敗');
    }
  });

  return {
    updateVendor: updateVendorMutation.mutate,
    isUpdating: updateVendorMutation.isPending
  };
};
