import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import VendorHeader from './VendorHeader';
import VendorSidebar from './VendorSidebar';
import { cn } from '@/lib/utils';

/**
 * 廠商後台佈局組件
 * 提供統一的廠商後台界面佈局
 */
const VendorLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂部導航 */}
      <VendorHeader 
        onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        sidebarOpen={sidebarOpen}
      />

      <div className="flex">
        {/* 側邊欄 */}
        <VendorSidebar 
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />

        {/* 主要內容區域 */}
        <main 
          className={cn(
            "flex-1 transition-all duration-300 ease-in-out",
            "lg:ml-64", // 桌面版固定側邊欄寬度
            sidebarOpen ? "ml-64" : "ml-0" // 移動版動態調整
          )}
        >
          {/* 內容容器 */}
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>

      {/* 移動版遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default VendorLayout;
