import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate } from 'react-router-dom';
import { Loader2, ShoppingCart, User, Phone, Car, FileText } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';

import { useCartStore } from '@/stores/cartStore';
import { useOrders } from '@/hooks/useOrders';
import { checkoutSchema, type CheckoutFormData } from '@/lib/validations/auth';

const CheckoutForm = () => {
  const navigate = useNavigate();
  const { items, totalAmount, clearCart } = useCartStore();
  const { createOrder, isCreatingOrder } = useOrders();

  const form = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      customerName: '',
      customerPhone: '',
      licensePlate: '',
      notes: '',
    },
  });

  const onSubmit = async (data: CheckoutFormData) => {
    if (items.length === 0) {
      return;
    }

    const orderData = {
      customer_name: data.customerName,
      customer_phone: data.customerPhone,
      license_plate: data.licensePlate || undefined,
      notes: data.notes || undefined,
      total_amount: totalAmount,
      items: items.map(item => ({
        product_id: item.id,
        vendor_id: item.vendor_id || 'default-vendor-id', // 暫時使用預設值
        product_name: item.name,
        quantity: item.quantity,
        unit_price: item.price,
        subtotal: item.price * item.quantity
      }))
    };

    createOrder(orderData, {
      onSuccess: (order) => {
        clearCart();
        navigate(`/orders/${order.id}`, { 
          state: { orderCreated: true }
        });
      }
    });
  };

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <ShoppingCart className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">購物車是空的</h2>
            <p className="text-gray-600 mb-6">請先添加商品到購物車再進行結帳</p>
            <Button onClick={() => navigate('/products')} className="w-full">
              前往購物
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">結帳</h1>
            <p className="text-gray-600">請填寫您的聯絡資訊以完成訂單</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左側：結帳表單 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>聯絡資訊</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* 客戶姓名 */}
                    <FormField
                      control={form.control}
                      name="customerName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center space-x-2">
                            <User className="w-4 h-4" />
                            <span>客戶姓名 *</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="請輸入您的姓名"
                              {...field}
                              disabled={isCreatingOrder}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 聯絡電話 */}
                    <FormField
                      control={form.control}
                      name="customerPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center space-x-2">
                            <Phone className="w-4 h-4" />
                            <span>聯絡電話 *</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="例如：**********"
                              {...field}
                              disabled={isCreatingOrder}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 車牌號碼 */}
                    <FormField
                      control={form.control}
                      name="licensePlate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center space-x-2">
                            <Car className="w-4 h-4" />
                            <span>車牌號碼（選填）</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="例如：ABC-1234"
                              {...field}
                              disabled={isCreatingOrder}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 備註 */}
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center space-x-2">
                            <FileText className="w-4 h-4" />
                            <span>備註（選填）</span>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="有任何特殊需求或備註請在此填寫..."
                              rows={3}
                              {...field}
                              disabled={isCreatingOrder}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Separator />

                    {/* 提交按鈕 */}
                    <Button
                      type="submit"
                      className="w-full bg-green-600 hover:bg-green-700"
                      disabled={isCreatingOrder}
                      size="lg"
                    >
                      {isCreatingOrder ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          處理中...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          確認訂單 NT$ {totalAmount.toLocaleString()}
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* 右側：訂單摘要 */}
            <Card>
              <CardHeader>
                <CardTitle>訂單摘要</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <img
                        src={item.image_url || '/placeholder.svg'}
                        alt={item.name}
                        className="w-12 h-12 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{item.name}</h4>
                        <p className="text-xs text-gray-500">
                          {item.vendor_name} | {item.quantity} {item.unit}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          NT$ {(item.price * item.quantity).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>商品小計</span>
                      <span>NT$ {totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>運費</span>
                      <span className="text-green-600">免費</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-semibold">
                      <span>總計</span>
                      <span className="text-green-600">
                        NT$ {totalAmount.toLocaleString()}
                      </span>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">取貨資訊</h4>
                    <p className="text-sm text-green-700">
                      請於訂單確認後前往果菜市場指定攤位取貨
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutForm;
