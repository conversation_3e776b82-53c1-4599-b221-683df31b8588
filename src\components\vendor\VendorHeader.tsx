import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Leaf,
  Menu,
  Bell,
  User,
  Settings,
  LogOut,
  Home,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useVendorAuth } from '@/hooks/useVendorAuth';
import { cn } from '@/lib/utils';

interface VendorHeaderProps {
  onMenuClick: () => void;
  sidebarOpen: boolean;
}

/**
 * 廠商後台頂部導航組件
 */
const VendorHeader: React.FC<VendorHeaderProps> = ({
  onMenuClick,
  sidebarOpen
}) => {
  const navigate = useNavigate();
  const { signOut } = useAuth();
  const { vendor } = useVendorAuth();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-30">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左側：Logo 和選單按鈕 */}
          <div className="flex items-center space-x-4">
            {/* 選單按鈕 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={onMenuClick}
              className="lg:hidden"
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* Logo */}
            <Link to="/vendor" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-market-green-500 to-market-green-600 rounded-lg flex items-center justify-center">
                <Leaf className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-gray-900">廠商後台</h1>
                <p className="text-xs text-gray-500">Vendor Dashboard</p>
              </div>
            </Link>
          </div>

          {/* 中間：廠商資訊 */}
          <div className="hidden md:flex items-center space-x-2">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {vendor?.name}
              </p>
              <p className="text-xs text-gray-500">
                {vendor?.location}
              </p>
            </div>
            <Badge 
              variant={vendor?.status === 'ACTIVE' ? 'default' : 'secondary'}
              className={cn(
                vendor?.status === 'ACTIVE' && 'bg-green-100 text-green-800',
                vendor?.status === 'PENDING' && 'bg-yellow-100 text-yellow-800',
                vendor?.status === 'SUSPENDED' && 'bg-red-100 text-red-800'
              )}
            >
              {vendor?.status === 'ACTIVE' && '已啟用'}
              {vendor?.status === 'PENDING' && '審核中'}
              {vendor?.status === 'SUSPENDED' && '已暫停'}
            </Badge>
          </div>

          {/* 右側：操作按鈕 */}
          <div className="flex items-center space-x-3">
            {/* 通知按鈕 */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                3
              </Badge>
            </Button>

            {/* 返回前台按鈕 */}
            <Button variant="outline" size="sm" asChild>
              <Link to="/">
                <Home className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">返回前台</span>
              </Link>
            </Button>

            {/* 用戶選單 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span className="hidden md:block">
                    {vendor?.name || '廠商'}
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end">
                <div className="px-2 py-1.5">
                  <p className="text-sm font-medium">{vendor?.name}</p>
                  <p className="text-xs text-gray-500">{vendor?.email}</p>
                </div>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => navigate('/vendor/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  廠商設定
                </DropdownMenuItem>
                
                <DropdownMenuItem onClick={() => navigate('/vendor/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  個人資料
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  登出
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

export default VendorHeader;
