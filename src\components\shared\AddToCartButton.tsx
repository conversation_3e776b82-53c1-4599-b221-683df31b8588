import React, { useState } from 'react';
import { Plus, Minus, ShoppingCart } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { useCartStore } from '@/stores/cartStore';
import { toast } from 'sonner';

interface AddToCartButtonProps {
  product: {
    id: string;
    name: string;
    price: number;
    unit: string;
    image_url?: string;
    vendor_name?: string;
    stock: number;
  };
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({ 
  product, 
  variant = 'default',
  className = '' 
}) => {
  const [quantity, setQuantity] = useState(1);
  const { addItem, items } = useCartStore();

  // 檢查商品是否已在購物車中
  const existingItem = items.find(item => item.id === product.id);
  const currentCartQuantity = existingItem?.quantity || 0;
  const maxQuantity = product.stock - currentCartQuantity;

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (maxQuantity <= 0) {
      toast.error('商品庫存不足');
      return;
    }

    addItem({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: quantity,
      unit: product.unit,
      image_url: product.image_url,
      vendor_name: product.vendor_name || '未知廠商'
    });
    
    toast.success(`已將 ${quantity} ${product.unit} ${product.name} 加入購物車`);
    setQuantity(1); // 重置數量
  };

  // 緊湊版本 - 用於商品卡片
  if (variant === 'compact') {
    return (
      <Button
        onClick={handleAddToCart}
        disabled={maxQuantity <= 0}
        size="sm"
        className={`bg-green-600 hover:bg-green-700 ${className}`}
      >
        <ShoppingCart className="w-4 h-4 mr-1" />
        加入購物車
      </Button>
    );
  }

  // 詳細版本 - 用於商品詳細頁面
  if (variant === 'detailed') {
    return (
      <div className={`space-y-4 ${className}`}>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            購買數量
          </label>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
            >
              <Minus className="w-4 h-4" />
            </Button>
            <span className="text-xl font-semibold w-16 text-center">
              {quantity}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuantityChange(1)}
              disabled={quantity >= maxQuantity}
            >
              <Plus className="w-4 h-4" />
            </Button>
            <span className="text-gray-500">{product.unit}</span>
          </div>
          {maxQuantity <= 0 && (
            <p className="text-sm text-red-600 mt-1">庫存不足</p>
          )}
          {currentCartQuantity > 0 && (
            <p className="text-sm text-gray-600 mt-1">
              購物車中已有 {currentCartQuantity} {product.unit}
            </p>
          )}
        </div>

        <Button
          onClick={handleAddToCart}
          disabled={maxQuantity <= 0}
          className="w-full bg-green-600 hover:bg-green-700"
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          加入購物車
        </Button>

        <div className="text-center text-sm text-gray-500">
          小計：NT$ {(product.price * quantity).toLocaleString()}
        </div>
      </div>
    );
  }

  // 預設版本
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-1">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuantityChange(-1)}
          disabled={quantity <= 1}
        >
          <Minus className="w-3 h-3" />
        </Button>
        <span className="text-sm font-medium w-8 text-center">
          {quantity}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleQuantityChange(1)}
          disabled={quantity >= maxQuantity}
        >
          <Plus className="w-3 h-3" />
        </Button>
      </div>
      
      <Button
        onClick={handleAddToCart}
        disabled={maxQuantity <= 0}
        size="sm"
        className="bg-green-600 hover:bg-green-700"
      >
        <ShoppingCart className="w-4 h-4 mr-1" />
        加入購物車
      </Button>
    </div>
  );
};

export default AddToCartButton;
