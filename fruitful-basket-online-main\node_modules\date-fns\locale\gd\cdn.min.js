var Q=function(H){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},Q(H)},W=function(H,B){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);B&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(H,N).enumerable})),X.push.apply(X,Z)}return X},K=function(H){for(var B=1;B<arguments.length;B++){var X=arguments[B]!=null?arguments[B]:{};B%2?W(Object(X),!0).forEach(function(Z){CC(H,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):W(Object(X)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(X,Z))})}return H},CC=function(H,B,X){if(B=GC(B),B in H)Object.defineProperty(H,B,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[B]=X;return H},GC=function(H){var B=UC(H,"string");return Q(B)=="symbol"?B:String(B)},UC=function(H,B){if(Q(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(H,B||"default");if(Q(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(B==="string"?String:Number)(H)};(function(H){var B=Object.defineProperty,X=function G(Y,C){for(var U in C)B(Y,U,{get:C[U],enumerable:!0,configurable:!0,set:function J(I){return C[U]=function(){return I}}})},Z={lessThanXSeconds:{one:"nas lugha na diog",other:"nas lugha na {{count}} diogan"},xSeconds:{one:"1 diog",two:"2 dhiog",twenty:"20 diog",other:"{{count}} diogan"},halfAMinute:"leth mhionaid",lessThanXMinutes:{one:"nas lugha na mionaid",other:"nas lugha na {{count}} mionaidean"},xMinutes:{one:"1 mionaid",two:"2 mhionaid",twenty:"20 mionaid",other:"{{count}} mionaidean"},aboutXHours:{one:"mu uair de th\xECde",other:"mu {{count}} uairean de th\xECde"},xHours:{one:"1 uair de th\xECde",two:"2 uair de th\xECde",twenty:"20 uair de th\xECde",other:"{{count}} uairean de th\xECde"},xDays:{one:"1 l\xE0",other:"{{count}} l\xE0"},aboutXWeeks:{one:"mu 1 seachdain",other:"mu {{count}} seachdainean"},xWeeks:{one:"1 seachdain",other:"{{count}} seachdainean"},aboutXMonths:{one:"mu mh\xECos",other:"mu {{count}} m\xECosan"},xMonths:{one:"1 m\xECos",other:"{{count}} m\xECosan"},aboutXYears:{one:"mu bhliadhna",other:"mu {{count}} bliadhnaichean"},xYears:{one:"1 bhliadhna",other:"{{count}} bliadhna"},overXYears:{one:"c\xF2rr is bliadhna",other:"c\xF2rr is {{count}} bliadhnaichean"},almostXYears:{one:"cha mh\xF2r bliadhna",other:"cha mh\xF2r {{count}} bliadhnaichean"}},N=function G(Y,C,U){var J,I=Z[Y];if(typeof I==="string")J=I;else if(C===1)J=I.one;else if(C===2&&!!I.two)J=I.two;else if(C===20&&!!I.twenty)J=I.twenty;else J=I.other.replace("{{count}}",String(C));if(U!==null&&U!==void 0&&U.addSuffix)if(U.comparison&&U.comparison>0)return"ann an "+J;else return"o chionn "+J;return J};function S(G){return function(){var Y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=Y.width?String(Y.width):G.defaultWidth,U=G.formats[C]||G.formats[G.defaultWidth];return U}}var $={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},D={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},M={full:"{{date}} 'aig' {{time}}",long:"{{date}} 'aig' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:S({formats:$,defaultWidth:"full"}),time:S({formats:D,defaultWidth:"full"}),dateTime:S({formats:M,defaultWidth:"full"})},L={lastWeek:"'mu dheireadh' eeee 'aig' p",yesterday:"'an-d\xE8 aig' p",today:"'an-diugh aig' p",tomorrow:"'a-m\xE0ireach aig' p",nextWeek:"eeee 'aig' p",other:"P"},f=function G(Y,C,U,J){return L[Y]};function E(G){return function(Y,C){var U=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",J;if(U==="formatting"&&G.formattingValues){var I=G.defaultFormattingWidth||G.defaultWidth,T=C!==null&&C!==void 0&&C.width?String(C.width):I;J=G.formattingValues[T]||G.formattingValues[I]}else{var x=G.defaultWidth,q=C!==null&&C!==void 0&&C.width?String(C.width):G.defaultWidth;J=G.values[q]||G.values[x]}var A=G.argumentCallback?G.argumentCallback(Y):Y;return J[A]}}var V={narrow:["R","A"],abbreviated:["RC","AD"],wide:["ro Chr\xECosta","anno domini"]},j={narrow:["1","2","3","4"],abbreviated:["C1","C2","C3","C4"],wide:["a' chiad chairteal","an d\xE0rna cairteal","an treas cairteal","an ceathramh cairteal"]},P={narrow:["F","G","M","G","C","\xD2","I","L","S","D","S","D"],abbreviated:["Faoi","Gear","M\xE0rt","Gibl","C\xE8it","\xD2gmh","Iuch","L\xF9n","Sult","D\xE0mh","Samh","D\xF9bh"],wide:["Am Faoilleach","An Gearran","Am M\xE0rt","An Giblean","An C\xE8itean","An t-\xD2gmhios","An t-Iuchar","An L\xF9nastal","An t-Sultain","An D\xE0mhair","An t-Samhain","An D\xF9bhlachd"]},v={narrow:["D","L","M","C","A","H","S"],short:["D\xF2","Lu","M\xE0","Ci","Ar","Ha","Sa"],abbreviated:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],wide:["Did\xF2mhnaich","Diluain","Dim\xE0irt","Diciadain","Diardaoin","Dihaoine","Disathairne"]},w={narrow:{am:"m",pm:"f",midnight:"m.o.",noon:"m.l.",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"},abbreviated:{am:"M.",pm:"F.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"},wide:{am:"m.",pm:"f.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"madainn",afternoon:"feasgar",evening:"feasgar",night:"oidhche"}},_={narrow:{am:"m",pm:"f",midnight:"m.o.",noon:"m.l.",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"},abbreviated:{am:"M.",pm:"F.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"},wide:{am:"m.",pm:"f.",midnight:"meadhan oidhche",noon:"meadhan l\xE0",morning:"sa mhadainn",afternoon:"feasgar",evening:"feasgar",night:"air an oidhche"}},F=function G(Y){var C=Number(Y),U=C%100;if(U>20||U<10)switch(U%10){case 1:return C+"d";case 2:return C+"na"}if(U===12)return C+"na";return C+"mh"},h={ordinalNumber:F,era:E({values:V,defaultWidth:"wide"}),quarter:E({values:j,defaultWidth:"wide",argumentCallback:function G(Y){return Y-1}}),month:E({values:P,defaultWidth:"wide"}),day:E({values:v,defaultWidth:"wide"}),dayPeriod:E({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function O(G){return function(Y){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=C.width,J=U&&G.matchPatterns[U]||G.matchPatterns[G.defaultMatchWidth],I=Y.match(J);if(!I)return null;var T=I[0],x=U&&G.parsePatterns[U]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(x)?k(x,function(z){return z.test(T)}):b(x,function(z){return z.test(T)}),A;A=G.valueCallback?G.valueCallback(q):q,A=C.valueCallback?C.valueCallback(A):A;var t=Y.slice(T.length);return{value:A,rest:t}}}var b=function G(Y,C){for(var U in Y)if(Object.prototype.hasOwnProperty.call(Y,U)&&C(Y[U]))return U;return},k=function G(Y,C){for(var U=0;U<Y.length;U++)if(C(Y[U]))return U;return};function m(G){return function(Y){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},U=Y.match(G.matchPattern);if(!U)return null;var J=U[0],I=Y.match(G.parsePattern);if(!I)return null;var T=G.valueCallback?G.valueCallback(I[0]):I[0];T=C.valueCallback?C.valueCallback(T):T;var x=Y.slice(J.length);return{value:T,rest:x}}}var y=/^(\d+)(d|na|tr|mh)?/i,c=/\d+/i,p={narrow:/^(r|a)/i,abbreviated:/^(r\.?\s?c\.?|r\.?\s?a\.?\s?c\.?|a\.?\s?d\.?|a\.?\s?c\.?)/i,wide:/^(ro Chrìosta|ron aois choitchinn|anno domini|aois choitcheann)/i},g={any:[/^b/i,/^(a|c)/i]},d={narrow:/^[1234]/i,abbreviated:/^c[1234]/i,wide:/^[1234](cd|na|tr|mh)? cairteal/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[fgmcòilsd]/i,abbreviated:/^(faoi|gear|màrt|gibl|cèit|ògmh|iuch|lùn|sult|dàmh|samh|dùbh)/i,wide:/^(am faoilleach|an gearran|am màrt|an giblean|an cèitean|an t-Ògmhios|an t-Iuchar|an lùnastal|an t-Sultain|an dàmhair|an t-Samhain|an dùbhlachd)/i},i={narrow:[/^f/i,/^g/i,/^m/i,/^g/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^s/i,/^d/i,/^s/i,/^d/i],any:[/^fa/i,/^ge/i,/^mà/i,/^gi/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^su/i,/^d/i,/^sa/i,/^d/i]},n={narrow:/^[dlmcahs]/i,short:/^(dò|lu|mà|ci|ar|ha|sa)/i,abbreviated:/^(did|dil|dim|dic|dia|dih|dis)/i,wide:/^(didòmhnaich|diluain|dimàirt|diciadain|diardaoin|dihaoine|disathairne)/i},s={narrow:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i],any:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i]},o={narrow:/^(a|p|mi|n|(san|aig) (madainn|feasgar|feasgar|oidhche))/i,any:/^([ap]\.?\s?m\.?|meadhan oidhche|meadhan là|(san|aig) (madainn|feasgar|feasgar|oidhche))/i},r={any:{am:/^m/i,pm:/^f/i,midnight:/^meadhan oidhche/i,noon:/^meadhan là/i,morning:/sa mhadainn/i,afternoon:/feasgar/i,evening:/feasgar/i,night:/air an oidhche/i}},a={ordinalNumber:m({matchPattern:y,parsePattern:c,valueCallback:function G(Y){return parseInt(Y,10)}}),era:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:O({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function G(Y){return Y+1}}),month:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"gd",formatDistance:N,formatLong:R,formatRelative:f,localize:h,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{gd:e})})})();

//# debugId=19F0FF6BA93EE83664756e2164756e21
