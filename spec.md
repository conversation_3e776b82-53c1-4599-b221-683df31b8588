# 果菜市場電商系統 - 技術規格文件

## 專案概述

### 專案名稱
果菜市場線上電商平台 (Fruitful Basket Online)

### 專案目標
建立一個專為大型果菜市場設計的B2B電商平台，讓廠商可以線上展示商品，客戶可以便捷下單，並提供完整的訂單管理系統。

### 核心特色
- 廠商自主管理商品與庫存
- 客戶快速瀏覽與下單
- 即時庫存管理
- 訂單狀態追蹤
- 響應式設計支援多裝置

## 技術架構

### 前端技術棧
- **框架**: React 18 + TypeScript
- **建構工具**: Vite
- **路由**: React Router DOM v6
- **UI框架**: Shadcn/ui + Radix UI
- **樣式**: Tailwind CSS
- **狀態管理**: Zustand
- **API管理**: TanStack React Query
- **表單處理**: React Hook Form + Zod
- **動畫**: Framer Motion
- **通知**: Sonner Toast

### 後端服務
- **資料庫**: Supabase (PostgreSQL)
- **認證**: Supabase Auth
- **檔案儲存**: Supabase Storage
- **即時功能**: Supabase Realtime

### 開發工具
- **程式碼品質**: ESLint + TypeScript
- **套件管理**: npm
- **版本控制**: Git

## 系統架構圖

```mermaid
graph TB
    subgraph "前端應用 (React + TypeScript)"
        A[客戶端界面] --> B[廠商後台]
        A --> C[商品瀏覽]
        A --> D[購物車]
        A --> E[訂單管理]
    end
    
    subgraph "狀態管理 (Zustand)"
        F[authStore] --> G[cartStore]
        G --> H[productStore]
        H --> I[orderStore]
    end
    
    subgraph "API層 (React Query)"
        J[useProducts] --> K[useOrders]
        K --> L[useVendors]
        L --> M[useAuth]
    end
    
    subgraph "後端服務 (Supabase)"
        N[PostgreSQL 資料庫] --> O[Auth 認證]
        O --> P[Storage 檔案儲存]
        P --> Q[Realtime 即時更新]
    end
    
    A --> F
    B --> F
    F --> J
    J --> N
```

## 資料庫設計

### ER圖

```mermaid
erDiagram
    VENDORS ||--o{ PRODUCTS : "擁有"
    VENDORS ||--o{ ORDER_ITEMS : "供應"
    PRODUCTS ||--o{ ORDER_ITEMS : "包含"
    ORDERS ||--o{ ORDER_ITEMS : "包含"
    CATEGORIES ||--o{ PRODUCTS : "分類"

    VENDORS {
        uuid id PK
        text name
        text phone
        text location
        text email UK
        text status
        timestamp created_at
        timestamp updated_at
    }

    PRODUCTS {
        uuid id PK
        uuid vendor_id FK
        text name
        text description
        decimal price
        integer stock
        text unit
        text image_url
        text category
        text status
        timestamp created_at
        timestamp updated_at
    }

    ORDERS {
        uuid id PK
        text customer_name
        text customer_phone
        text license_plate
        text notes
        decimal total_amount
        text status
        timestamp created_at
        timestamp updated_at
    }

    ORDER_ITEMS {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        uuid vendor_id FK
        text product_name
        decimal quantity
        decimal unit_price
        decimal subtotal
        timestamp created_at
    }

    CATEGORIES {
        uuid id PK
        text name UK
        text description
        integer sort_order
        timestamp created_at
    }
```

### 資料表詳細設計

#### vendors (廠商資料表)
| 欄位 | 類型 | 約束 | 說明 |
|------|------|------|------|
| id | UUID | PK | 廠商唯一識別碼 |
| name | TEXT | NOT NULL | 廠商名稱 |
| phone | TEXT | NOT NULL | 聯絡電話 |
| location | TEXT | NOT NULL | 攤位位置 |
| email | TEXT | UNIQUE, NOT NULL | 電子郵件 |
| status | TEXT | CHECK | 狀態: PENDING/ACTIVE/SUSPENDED |
| created_at | TIMESTAMP | DEFAULT NOW() | 建立時間 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新時間 |

#### products (商品資料表)
| 欄位 | 類型 | 約束 | 說明 |
|------|------|------|------|
| id | UUID | PK | 商品唯一識別碼 |
| vendor_id | UUID | FK | 廠商識別碼 |
| name | TEXT | NOT NULL | 商品名稱 |
| description | TEXT | | 商品描述 |
| price | DECIMAL(10,2) | NOT NULL | 商品價格 |
| stock | INTEGER | DEFAULT 0 | 庫存數量 |
| unit | TEXT | CHECK | 計價單位: KG/G/PIECE/BOX/JIN |
| image_url | TEXT | | 商品圖片URL |
| category | TEXT | NOT NULL | 商品分類 |
| status | TEXT | CHECK | 狀態: ACTIVE/INACTIVE |
| created_at | TIMESTAMP | DEFAULT NOW() | 建立時間 |

## 系統流程圖

### 客戶下單流程

```mermaid
sequenceDiagram
    participant C as 客戶
    participant UI as 前端界面
    participant API as API層
    participant DB as 資料庫
    participant V as 廠商

    C->>UI: 瀏覽商品
    UI->>API: 獲取商品列表
    API->>DB: 查詢商品資料
    DB-->>API: 返回商品資料
    API-->>UI: 返回商品列表
    UI-->>C: 顯示商品

    C->>UI: 加入購物車
    UI->>UI: 更新購物車狀態

    C->>UI: 填寫訂單資訊
    UI->>API: 提交訂單
    API->>DB: 建立訂單記錄
    API->>DB: 建立訂單明細
    API->>DB: 更新商品庫存
    DB-->>API: 確認訂單建立
    API-->>UI: 返回訂單確認
    UI-->>C: 顯示訂單成功

    Note over V: 廠商收到新訂單通知
```

### 廠商管理商品流程

```mermaid
sequenceDiagram
    participant V as 廠商
    participant UI as 廠商後台
    participant API as API層
    participant DB as 資料庫
    participant S as 檔案儲存

    V->>UI: 登入後台
    UI->>API: 驗證身份
    API->>DB: 查詢廠商資料
    DB-->>API: 返回廠商資料
    API-->>UI: 返回認證結果
    UI-->>V: 顯示後台首頁

    V->>UI: 新增商品
    UI->>S: 上傳商品圖片
    S-->>UI: 返回圖片URL
    UI->>API: 提交商品資料
    API->>DB: 建立商品記錄
    DB-->>API: 確認建立成功
    API-->>UI: 返回建立結果
    UI-->>V: 顯示成功訊息

    V->>UI: 查看訂單
    UI->>API: 獲取廠商訂單
    API->>DB: 查詢相關訂單
    DB-->>API: 返回訂單資料
    API-->>UI: 返回訂單列表
    UI-->>V: 顯示訂單列表

    V->>UI: 更新訂單狀態
    UI->>API: 更新訂單狀態
    API->>DB: 更新訂單記錄
    DB-->>API: 確認更新成功
    API-->>UI: 返回更新結果
    UI-->>V: 顯示更新成功
```

## 組件架構圖

```mermaid
graph TD
    subgraph "頁面層 (Pages)"
        A[Index 首頁] --> B[Products 商品頁]
        B --> C[NotFound 404頁]
    end

    subgraph "佈局組件 (Layout)"
        D[Header 頁首] --> E[Footer 頁尾]
    end

    subgraph "共享組件 (Shared)"
        F[ProductCard 商品卡片] --> G[ProductGrid 商品網格]
        G --> H[CartSidebar 購物車側邊欄]
        H --> I[FloatingCartButton 浮動購物車按鈕]
        I --> J[CategoryFilter 分類篩選]
        J --> K[HeroSection 主視覺區]
        K --> L[MarketStats 市場統計]
        L --> M[FeaturedProducts 精選商品]
    end

    subgraph "UI組件 (UI)"
        N[Button 按鈕] --> O[Card 卡片]
        O --> P[Dialog 對話框]
        P --> Q[Form 表單]
        Q --> R[Input 輸入框]
        R --> S[Toast 通知]
    end

    A --> D
    A --> F
    B --> F
    F --> N
```

## 狀態管理架構

### Zustand Store 設計

```mermaid
graph LR
    subgraph "認證狀態 (authStore)"
        A1[user] --> A2[isAuthenticated]
        A2 --> A3[loading]
    end

    subgraph "購物車狀態 (cartStore)"
        B1[items] --> B2[totalItems]
        B2 --> B3[totalAmount]
        B3 --> B4[isOpen]
    end

    subgraph "商品狀態 (productStore)"
        C1[filters] --> C2[viewMode]
        C2 --> C3[sortBy]
    end

    subgraph "訂單狀態 (orderStore)"
        D1[currentOrder] --> D2[orderHistory]
    end

    A1 --> B1
    B1 --> C1
    C1 --> D1
```

### API Hook 架構

```mermaid
graph TD
    subgraph "產品相關 Hooks"
        E1[useProducts] --> E2[useCategories]
        E2 --> E3[useVendors]
    end

    subgraph "訂單相關 Hooks"
        F1[useOrders] --> F2[useCart]
    end

    subgraph "認證相關 Hooks"
        G1[useAuth] --> G2[useImageUpload]
    end

    subgraph "工具 Hooks"
        H1[useLocalStorage] --> H2[use-mobile]
        H2 --> H3[use-toast]
    end

    E1 --> F1
    F1 --> G1
    G1 --> H1
```

## 功能模組設計

### 客戶端功能模組

#### 1. 商品瀏覽模組
- **組件**: ProductCard, ProductGrid, CategoryFilter
- **功能**:
  - 商品列表展示
  - 分類篩選
  - 搜尋功能
  - 排序功能
- **API**: useProducts, useCategories

#### 2. 購物車模組
- **組件**: CartSidebar, FloatingCartButton
- **功能**:
  - 加入購物車
  - 數量調整
  - 移除商品
  - 購物車持久化
- **Store**: cartStore

#### 3. 訂單模組
- **組件**: CheckoutForm, OrderConfirmation
- **功能**:
  - 客戶資訊填寫
  - 訂單提交
  - 訂單確認
- **API**: useOrders

### 廠商端功能模組

#### 1. 商品管理模組
- **功能**:
  - 商品新增/編輯/刪除
  - 庫存管理
  - 圖片上傳
  - 批量操作

#### 2. 訂單管理模組
- **功能**:
  - 訂單查看
  - 狀態更新
  - 訂單篩選
  - 訂單列印

#### 3. 統計分析模組
- **功能**:
  - 銷售統計
  - 商品分析
  - 收益報表

## 安全性設計

### 資料庫安全
- **Row Level Security (RLS)**: 確保廠商只能存取自己的資料
- **API 權限控制**: 基於 Supabase Auth 的角色權限
- **資料驗證**: 前後端雙重驗證

### 前端安全
- **輸入驗證**: 使用 Zod 進行表單驗證
- **XSS 防護**: React 內建防護機制
- **CSRF 防護**: Supabase 內建防護

## 效能優化

### 前端優化
- **程式碼分割**: React.lazy 動態載入
- **圖片優化**: 響應式圖片載入
- **快取策略**: React Query 快取機制
- **虛擬滾動**: 大量資料展示優化

### 資料庫優化
- **索引設計**: 關鍵查詢欄位建立索引
- **查詢優化**: 避免 N+1 查詢問題
- **分頁載入**: 大量資料分頁處理

## 測試策略

### 單元測試
- **組件測試**: React Testing Library
- **Hook 測試**: 自定義 Hook 測試
- **工具函數測試**: Jest 單元測試

### 整合測試
- **API 測試**: Supabase 整合測試
- **端到端測試**: Playwright 或 Cypress

## 部署架構

### 開發環境
- **本地開發**: Vite 開發伺服器
- **資料庫**: Supabase 開發環境
- **熱重載**: Vite HMR

### 生產環境
- **前端部署**: Vercel 或 Netlify
- **資料庫**: Supabase 生產環境
- **CDN**: 靜態資源 CDN 加速

## 監控與維護

### 錯誤監控
- **前端錯誤**: Error Boundary 捕獲
- **API 錯誤**: 統一錯誤處理
- **使用者行為**: 關鍵操作日誌

### 效能監控
- **載入時間**: Core Web Vitals
- **API 回應時間**: React Query DevTools
- **資料庫效能**: Supabase 監控面板

## 新增功能模組設計

### 1. 廠商後台管理系統

#### 1.1 系統架構
```mermaid
graph TD
    subgraph "廠商後台架構"
        A[VendorDashboard 主控台] --> B[VendorLayout 佈局]
        B --> C[VendorSidebar 側邊欄]
        B --> D[VendorHeader 頂部欄]

        A --> E[ProductManagement 商品管理]
        A --> F[OrderManagement 訂單管理]
        A --> G[Analytics 數據分析]
        A --> H[Settings 設定]

        E --> I[ProductForm 商品表單]
        E --> J[ProductTable 商品列表]
        E --> K[BulkActions 批量操作]

        F --> L[OrderTable 訂單列表]
        F --> M[OrderDetail 訂單詳情]
        F --> N[StatusUpdate 狀態更新]
    end
```

#### 1.2 權限控制設計
- **角色定義**: VENDOR, ADMIN, CUSTOMER
- **路由保護**: VendorProtectedRoute 組件
- **資料存取**: RLS 政策確保廠商只能存取自己的資料
- **操作權限**: 基於廠商狀態的功能限制

#### 1.3 核心功能模組

##### 商品管理模組
- **商品 CRUD**: 新增、編輯、刪除、批量操作
- **庫存管理**: 即時庫存更新、低庫存警告
- **商品狀態**: 上架/下架、草稿模式
- **分類管理**: 商品分類設定

##### 訂單管理模組
- **訂單查看**: 分頁列表、詳細資訊
- **狀態管理**: 訂單狀態更新、處理流程
- **篩選搜尋**: 多條件篩選、時間範圍
- **批量處理**: 批量狀態更新

### 2. 圖片上傳功能

#### 2.1 技術架構
```mermaid
graph LR
    subgraph "圖片上傳流程"
        A[選擇圖片] --> B[前端驗證]
        B --> C[圖片壓縮]
        C --> D[上傳到 Supabase Storage]
        D --> E[獲取 URL]
        E --> F[儲存到資料庫]
    end

    subgraph "圖片處理"
        G[原始圖片] --> H[尺寸調整]
        H --> I[品質壓縮]
        I --> J[格式轉換]
        J --> K[多尺寸生成]
    end
```

#### 2.2 功能特性
- **多格式支援**: JPEG, PNG, WebP
- **自動壓縮**: 智能品質調整
- **多尺寸生成**: 縮圖、中圖、大圖
- **拖拽上傳**: 直觀的上傳體驗
- **進度顯示**: 上傳進度條
- **錯誤處理**: 上傳失敗重試機制

#### 2.3 儲存策略
- **路徑結構**: `/vendors/{vendor_id}/products/{product_id}/`
- **檔名規則**: `{timestamp}_{random}_{original_name}`
- **存取權限**: 公開讀取，廠商寫入
- **CDN 加速**: Supabase Storage CDN

### 3. 通知系統

#### 3.1 系統架構
```mermaid
graph TD
    subgraph "通知系統架構"
        A[NotificationService 通知服務] --> B[RealtimeNotifications 即時通知]
        A --> C[EmailNotifications 郵件通知]
        A --> D[InAppNotifications 應用內通知]

        B --> E[Supabase Realtime]
        C --> F[Email Templates]
        D --> G[NotificationCenter 通知中心]

        G --> H[NotificationList 通知列表]
        G --> I[NotificationItem 通知項目]
        G --> J[NotificationSettings 通知設定]
    end
```

#### 3.2 通知類型
- **訂單通知**: 新訂單、狀態變更、取消訂單
- **庫存通知**: 低庫存警告、缺貨提醒
- **系統通知**: 系統維護、功能更新
- **促銷通知**: 特價活動、新品上架

#### 3.3 通知渠道
- **即時通知**: WebSocket 推送
- **應用內通知**: 通知中心顯示
- **郵件通知**: 重要事件郵件
- **推播通知**: PWA 推播 (未來擴展)

#### 3.4 通知管理
- **偏好設定**: 用戶自定義通知類型
- **免打擾模式**: 時間段設定
- **通知歷史**: 已讀/未讀狀態
- **批量操作**: 全部標記已讀

### 4. 數據統計分析

#### 4.1 分析架構
```mermaid
graph TD
    subgraph "數據分析系統"
        A[AnalyticsDashboard 分析儀表板] --> B[SalesAnalytics 銷售分析]
        A --> C[ProductAnalytics 商品分析]
        A --> D[CustomerAnalytics 客戶分析]
        A --> E[RevenueAnalytics 收益分析]

        B --> F[SalesChart 銷售圖表]
        B --> G[SalesTrends 銷售趨勢]

        C --> H[ProductRanking 商品排行]
        C --> I[StockAnalysis 庫存分析]

        D --> J[CustomerBehavior 客戶行為]
        D --> K[OrderPatterns 訂單模式]

        E --> L[RevenueChart 收益圖表]
        E --> M[ProfitAnalysis 利潤分析]
    end
```

#### 4.2 統計指標

##### 銷售統計
- **銷售額**: 日/週/月/年銷售額
- **訂單量**: 訂單數量統計
- **平均客單價**: AOV 分析
- **銷售趨勢**: 時間序列分析

##### 商品分析
- **熱銷商品**: 銷量排行榜
- **庫存週轉**: 庫存週轉率
- **商品評價**: 評分統計
- **價格分析**: 價格區間分布

##### 客戶分析
- **客戶分群**: RFM 分析
- **購買行為**: 購買頻率、偏好
- **客戶價值**: LTV 分析
- **流失分析**: 客戶流失率

##### 收益分析
- **毛利分析**: 商品毛利率
- **成本分析**: 運營成本統計
- **利潤趨勢**: 利潤變化趨勢
- **ROI 分析**: 投資回報率

#### 4.3 報表功能
- **自定義報表**: 靈活的報表配置
- **定期報表**: 自動生成週/月報
- **數據導出**: Excel/PDF 導出
- **數據視覺化**: 豐富的圖表類型

#### 4.4 數據處理
- **實時計算**: 關鍵指標實時更新
- **批量處理**: 歷史數據分析
- **數據快取**: Redis 快取熱點數據
- **數據同步**: 定期數據同步任務

## 資料庫擴展設計

### 新增資料表

#### notifications (通知資料表)
| 欄位 | 類型 | 約束 | 說明 |
|------|------|------|------|
| id | UUID | PK | 通知唯一識別碼 |
| user_id | UUID | FK | 接收用戶ID |
| vendor_id | UUID | FK | 相關廠商ID (可選) |
| type | TEXT | NOT NULL | 通知類型 |
| title | TEXT | NOT NULL | 通知標題 |
| message | TEXT | NOT NULL | 通知內容 |
| data | JSONB | | 額外數據 |
| read_at | TIMESTAMP | | 已讀時間 |
| created_at | TIMESTAMP | DEFAULT NOW() | 建立時間 |

#### analytics_events (分析事件表)
| 欄位 | 類型 | 約束 | 說明 |
|------|------|------|------|
| id | UUID | PK | 事件唯一識別碼 |
| vendor_id | UUID | FK | 廠商ID |
| event_type | TEXT | NOT NULL | 事件類型 |
| event_data | JSONB | NOT NULL | 事件數據 |
| created_at | TIMESTAMP | DEFAULT NOW() | 事件時間 |

#### vendor_settings (廠商設定表)
| 欄位 | 類型 | 約束 | 說明 |
|------|------|------|------|
| id | UUID | PK | 設定唯一識別碼 |
| vendor_id | UUID | FK | 廠商ID |
| setting_key | TEXT | NOT NULL | 設定鍵 |
| setting_value | JSONB | NOT NULL | 設定值 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新時間 |

## API 設計擴展

### 廠商後台 API
- `GET /api/vendor/dashboard` - 獲取儀表板數據
- `GET /api/vendor/products` - 獲取廠商商品列表
- `POST /api/vendor/products` - 新增商品
- `PUT /api/vendor/products/:id` - 更新商品
- `DELETE /api/vendor/products/:id` - 刪除商品
- `GET /api/vendor/orders` - 獲取廠商訂單
- `PUT /api/vendor/orders/:id/status` - 更新訂單狀態

### 圖片上傳 API
- `POST /api/upload/image` - 上傳圖片
- `DELETE /api/upload/image/:id` - 刪除圖片
- `GET /api/upload/images` - 獲取圖片列表

### 通知系統 API
- `GET /api/notifications` - 獲取通知列表
- `PUT /api/notifications/:id/read` - 標記已讀
- `PUT /api/notifications/read-all` - 全部標記已讀
- `GET /api/notification-settings` - 獲取通知設定
- `PUT /api/notification-settings` - 更新通知設定

### 數據分析 API
- `GET /api/analytics/sales` - 銷售統計
- `GET /api/analytics/products` - 商品分析
- `GET /api/analytics/customers` - 客戶分析
- `GET /api/analytics/revenue` - 收益分析
- `GET /api/analytics/reports/:type` - 生成報表
