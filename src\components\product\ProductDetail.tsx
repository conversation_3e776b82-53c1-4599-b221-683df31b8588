import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Heart, Share2, MapPin, Phone, Star } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { useProducts } from '@/hooks/useProducts';
import AddToCartButton from '@/components/shared/AddToCartButton';
import { toast } from 'sonner';

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState(0);

  // 模擬商品資料 - 實際應該從 API 獲取
  const product = {
    id: id || '1',
    name: '有機高麗菜',
    description: '來自雲林的新鮮有機高麗菜，無農藥殘留，口感清脆甘甜。採用自然農法種植，營養豐富，是您健康飲食的最佳選擇。',
    price: 45,
    stock: 150,
    unit: 'KG',
    category: '葉菜類',
    status: 'ACTIVE' as const,
    image_url: '/placeholder.svg',
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    vendor: {
      id: 'vendor-1',
      name: '綠野農場',
      location: 'A區第3排第5號',
      phone: '0912-345-678',
      rating: 4.8,
      totalReviews: 156
    },
    nutritionInfo: {
      calories: '25 kcal/100g',
      protein: '1.3g',
      carbs: '5.8g',
      fiber: '2.5g',
      vitaminC: '36.6mg'
    },
    features: ['有機認證', '無農藥', '當日採收', '產地直送'],
    created_at: '2024-01-15T08:00:00Z'
  };



  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('商品連結已複製到剪貼簿');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 返回按鈕 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <Button
            variant="ghost"
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回</span>
          </Button>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 商品圖片區域 */}
          <div className="space-y-4">
            {/* 主圖片 */}
            <div className="aspect-square bg-white rounded-lg overflow-hidden border">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* 縮圖 */}
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-green-500' : 'border-gray-200'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* 商品資訊區域 */}
          <div className="space-y-6">
            {/* 基本資訊 */}
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Badge variant="secondary">{product.category}</Badge>
                {product.features.map((feature, index) => (
                  <Badge key={index} variant="outline" className="text-green-600 border-green-600">
                    {feature}
                  </Badge>
                ))}
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-3xl font-bold text-green-600">
                  NT$ {product.price}
                </span>
                <span className="text-gray-500">/ {product.unit}</span>
              </div>

              <p className="text-gray-600 leading-relaxed">{product.description}</p>
            </div>

            {/* 廠商資訊 */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-lg">{product.vendor.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{product.vendor.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Phone className="w-4 h-4" />
                        <span>{product.vendor.phone}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold">{product.vendor.rating}</span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {product.vendor.totalReviews} 則評價
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 庫存狀態 */}
            <div>
              {product.stock > 0 ? (
                <Alert>
                  <AlertDescription>
                    庫存充足，剩餘 {product.stock} {product.unit}
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert variant="destructive">
                  <AlertDescription>
                    商品暫時缺貨
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* 購買區域 */}
            <div className="space-y-4">
              <AddToCartButton
                product={{
                  id: product.id,
                  name: product.name,
                  price: product.price,
                  unit: product.unit,
                  image_url: product.image_url,
                  vendor_name: product.vendor.name,
                  stock: product.stock
                }}
                variant="detailed"
              />

              <div className="flex space-x-3">
                <Button variant="outline" size="icon">
                  <Heart className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={handleShare}>
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 詳細資訊區域 */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 營養資訊 */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-4">營養資訊</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>熱量</span>
                  <span className="font-medium">{product.nutritionInfo.calories}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span>蛋白質</span>
                  <span className="font-medium">{product.nutritionInfo.protein}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span>碳水化合物</span>
                  <span className="font-medium">{product.nutritionInfo.carbs}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span>膳食纖維</span>
                  <span className="font-medium">{product.nutritionInfo.fiber}</span>
                </div>
                <Separator />
                <div className="flex justify-between">
                  <span>維生素C</span>
                  <span className="font-medium">{product.nutritionInfo.vitaminC}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 商品特色 */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-4">商品特色</h3>
              <div className="space-y-3">
                {product.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t">
                <h4 className="font-medium mb-2">保存方式</h4>
                <p className="text-sm text-gray-600">
                  請放置於冰箱冷藏保存，建議在3-5天內食用完畢以確保最佳口感。
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
