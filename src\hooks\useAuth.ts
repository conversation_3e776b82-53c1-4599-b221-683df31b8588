
import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';

interface VendorData {
  name: string;
  phone: string;
  location: string;
  email: string;
}

export const useAuth = () => {
  const { user, setUser, setLoading, clearAuth } = useAuthStore();
  const [loading, setLoadingState] = useState(true);

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      setLoading(true);
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoadingState(false);
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        setLoadingState(false);
      }
    );

    return () => subscription.unsubscribe();
  }, [setUser, setLoading]);

  const signUp = async (email: string, password: string, vendorData: VendorData) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/`
        }
      });

      if (error) throw error;

      if (data.user) {
        // Insert vendor data with PENDING status
        const { error: vendorError } = await supabase.from('vendors').insert({
          id: data.user.id,
          ...vendorData,
          status: 'PENDING' // 新註冊的廠商需要等待審核
        });

        if (vendorError) throw vendorError;

        toast.success('廠商註冊申請已提交！請等待管理員審核，審核通過後將會通知您。');
      }

      return { data, error: null };
    } catch (error: any) {
      console.error('Sign up error:', error);
      toast.error(error.message || '註冊失敗');
      return { data: null, error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      
      toast.success('登入成功！');
      return { data, error: null };
    } catch (error: any) {
      console.error('Sign in error:', error);
      toast.error(error.message || '登入失敗');
      return { data: null, error };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      clearAuth();
      toast.success('已成功登出');
      return { error: null };
    } catch (error: any) {
      console.error('Sign out error:', error);
      toast.error(error.message || '登出失敗');
      return { error };
    }
  };

  return {
    user,
    loading,
    signUp,
    signIn,
    signOut,
    isAuthenticated: !!user
  };
};
