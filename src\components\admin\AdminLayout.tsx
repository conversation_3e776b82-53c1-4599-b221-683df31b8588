import React from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  Store, 
  ShoppingCart, 
  Users, 
  Settings,
  LogOut,
  Shield
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

const AdminLayout = () => {
  const location = useLocation();
  const { signOut } = useAuth();

  const navigation = [
    {
      name: '控制台',
      href: '/admin',
      icon: LayoutDashboard,
      current: location.pathname === '/admin'
    },
    {
      name: '廠商審核',
      href: '/admin/vendors',
      icon: Store,
      current: location.pathname === '/admin/vendors'
    },
    {
      name: '商品管理',
      href: '/admin/products',
      icon: ShoppingCart,
      current: location.pathname === '/admin/products'
    },
    {
      name: '訂單管理',
      href: '/admin/orders',
      icon: Users,
      current: location.pathname === '/admin/orders'
    },
    {
      name: '系統設定',
      href: '/admin/settings',
      icon: Settings,
      current: location.pathname === '/admin/settings'
    }
  ];

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 側邊欄 */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-center border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">管理後台</span>
            </div>
          </div>

          {/* 導航選單 */}
          <nav className="flex-1 space-y-1 px-4 py-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    item.current
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  )}
                >
                  <Icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      item.current ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    )}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* 底部操作 */}
          <div className="border-t border-gray-200 p-4">
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-700 hover:bg-gray-100"
              onClick={handleSignOut}
            >
              <LogOut className="mr-3 h-5 w-5" />
              登出
            </Button>
          </div>
        </div>
      </div>

      {/* 主要內容區域 */}
      <div className="pl-64">
        {/* 頂部導航欄 */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold text-gray-900">
                果菜市場管理系統
              </h1>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  管理員模式
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* 頁面內容 */}
        <main className="p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
