# 果菜市場電商系統 - 開發任務清單

## 專案狀態概覽

### 已完成功能 ✅
- [x] 基礎專案架構設定 (Vite + React + TypeScript)
- [x] UI 組件庫整合 (Shadcn/ui + Radix UI)
- [x] 狀態管理設定 (Zustand)
- [x] API 管理設定 (TanStack React Query)
- [x] 資料庫連接 (Supabase)
- [x] 基礎路由設定 (React Router)
- [x] 樣式系統設定 (Tailwind CSS)
- [x] 資料庫設計與建立 (所有資料表)
- [x] 資料庫安全設定 (RLS, Policies, Triggers)
- [x] 基礎 API Hooks (useProducts, useVendors, useCategories)
- [x] 核心組件架構 (ProductCard, CartStore 等)
- [x] 認證系統完整實作 (登入/註冊/廠商註冊)
- [x] 商品瀏覽功能完整實作 (詳細頁面、搜尋、篩選)
- [x] 購物車與訂單系統完整實作 (結帳流程、訂單管理、狀態追蹤)

### 進行中功能 🚧
- [ ] 廠商後台管理系統

### 待開發功能 📋
- [ ] 廠商後台管理
- [ ] 圖片上傳功能
- [ ] 通知系統
- [ ] 數據統計分析

## 🚀 立即執行任務 (下一步重點)

### 優先級 1: 認證系統 UI 完善 ✅
- [x] **建立認證頁面**
  - [x] 建立 `/auth/login` 登入頁面
  - [x] 建立 `/auth/register` 註冊頁面
  - [x] 建立 `/auth/vendor-register` 廠商註冊頁面
  - [x] 更新路由設定

- [x] **建立認證表單組件**
  - [x] LoginForm 組件 (使用 React Hook Form + Zod)
  - [x] RegisterForm 組件
  - [x] VendorRegisterForm 組件
  - [x] 表單驗證規則設定

- [x] **整合 Header 認證功能**
  - [x] 更新 Header 組件顯示登入狀態
  - [x] 實作登入/登出功能
  - [x] 添加用戶選單

- [x] **額外完成項目**
  - [x] 建立 ProtectedRoute 路由保護組件
  - [x] 整合購物車數量顯示

### 優先級 2: 商品瀏覽功能完善 ✅
- [x] **商品詳細頁面**
  - [x] 建立 ProductDetail 組件
  - [x] 建立 `/products/:id` 路由
  - [x] 實作商品詳細資訊顯示

- [x] **商品篩選與搜尋**
  - [x] 完善 ProductFilter 組件
  - [x] 實作 ProductSearch 組件
  - [x] 整合篩選與搜尋功能

- [x] **AddToCart 按鈕組件**
  - [x] 建立可重複使用的 AddToCartButton 組件
  - [x] 支援多種顯示模式 (compact, default, detailed)
  - [x] 整合庫存檢查和購物車狀態

### 優先級 3: 購物車與訂單流程 ✅
- [x] **購物車功能完善**
  - [x] 優化 AddToCartButton 組件
  - [x] 完善 CartSidebar 組件
  - [x] 整合結帳功能

- [x] **訂單流程**
  - [x] 建立 CheckoutForm 組件
  - [x] 建立 `/checkout` 結帳頁面
  - [x] 實作訂單提交功能

- [x] **訂單管理系統**
  - [x] 建立 OrderDetail 組件
  - [x] 建立 OrderList 組件
  - [x] 建立 `/orders` 和 `/orders/:id` 頁面
  - [x] 實作訂單狀態追蹤
  - [x] 實作訂單取消功能
  - [x] 擴展 useOrders Hook 功能

## 第一階段：核心基礎功能 (MVP)

### 1.1 資料庫設計與設定
- [x] **Supabase 專案建立**
  - [x] 建立 Supabase 專案
  - [x] 設定環境變數
  - [x] 建立資料庫連接

- [x] **資料表建立**
  - [x] 建立 vendors 資料表
  - [x] 建立 products 資料表
  - [x] 建立 orders 資料表
  - [x] 建立 order_items 資料表
  - [x] 建立 categories 資料表

- [x] **資料庫安全設定**
  - [x] 設定 Row Level Security (RLS)
  - [x] 建立安全政策 (Policies)
  - [x] 設定觸發器 (Triggers)
  - [x] 建立資料庫函數

### 1.2 認證系統
- [x] **Supabase Auth 設定**
  - [x] 設定 Auth 提供者
  - [ ] 建立註冊/登入表單
  - [x] 實作 useAuth Hook
  - [x] 設定認證狀態管理

- [ ] **廠商註冊流程**
  - [ ] 建立廠商註冊表單
  - [ ] 實作表單驗證
  - [x] 連接廠商資料表
  - [ ] 設定審核機制

### 1.3 商品管理系統
- [ ] **商品資料結構**
  - [ ] 定義商品 TypeScript 介面
  - [ ] 建立商品 API Hooks
  - [ ] 實作商品 CRUD 操作

- [ ] **商品展示組件**
  - [x] ProductCard 組件
  - [x] ProductGrid 組件
  - [ ] ProductDetail 組件
  - [ ] ProductFilter 組件
  - [ ] ProductSearch 組件

### 1.4 購物車系統
- [x] **購物車狀態管理**
  - [x] 建立 cartStore
  - [x] 實作購物車邏輯
  - [x] 設定本地儲存持久化

- [ ] **購物車 UI 組件**
  - [x] CartSidebar 組件
  - [x] FloatingCartButton 組件
  - [ ] CartItem 組件優化
  - [ ] CartSummary 組件
  - [ ] AddToCartButton 組件

### 1.5 訂單系統
- [ ] **訂單資料結構**
  - [x] 定義訂單 TypeScript 介面
  - [ ] 建立訂單 API Hooks
  - [ ] 實作訂單 CRUD 操作

- [ ] **訂單流程組件**
  - [ ] CheckoutForm 組件
  - [ ] OrderConfirmation 組件
  - [ ] OrderSummary 組件
  - [ ] OrderHistory 組件

## 第二階段：廠商後台功能

### 2.1 廠商後台架構
- [ ] **後台佈局設計**
  - [ ] VendorLayout 組件
  - [ ] VendorHeader 組件
  - [ ] VendorSidebar 組件
  - [ ] 導航選單設計

- [ ] **權限控制**
  - [ ] 廠商身份驗證
  - [ ] 路由保護機制
  - [ ] 資料存取權限

### 2.2 商品管理後台
- [ ] **商品管理介面**
  - [ ] ProductForm 組件 (新增/編輯)
  - [ ] ProductTable 組件 (列表)
  - [ ] ProductStatus 組件 (狀態切換)
  - [ ] BulkActions 組件 (批量操作)

- [ ] **圖片上傳功能**
  - [ ] 設定 Supabase Storage
  - [ ] 實作 useImageUpload Hook
  - [ ] 建立圖片上傳組件
  - [ ] 圖片壓縮與優化

### 2.3 訂單管理後台
- [ ] **訂單管理介面**
  - [ ] OrderTable 組件
  - [ ] OrderDetail 組件
  - [ ] OrderStatusUpdate 組件
  - [ ] OrderFilter 組件

- [ ] **訂單處理流程**
  - [ ] 訂單狀態更新
  - [ ] 庫存自動扣減
  - [ ] 訂單通知機制

### 2.4 統計分析功能
- [ ] **數據統計**
  - [ ] DashboardStats 組件
  - [ ] 銷售統計圖表
  - [ ] 商品分析報表
  - [ ] 收益統計

- [ ] **報表功能**
  - [ ] 日/週/月報表
  - [ ] 商品銷售排行
  - [ ] 客戶分析
  - [ ] 導出功能

## 第三階段：進階功能

### 3.1 搜尋與篩選
- [ ] **進階搜尋**
  - [ ] 全文搜尋功能
  - [ ] 多條件篩選
  - [ ] 搜尋結果排序
  - [ ] 搜尋歷史記錄

- [ ] **分類管理**
  - [ ] 分類階層設計
  - [ ] 分類管理介面
  - [ ] 動態分類載入

### 3.2 通知系統
- [ ] **即時通知**
  - [ ] Supabase Realtime 整合
  - [ ] 新訂單通知
  - [ ] 庫存警告通知
  - [ ] 系統公告

- [ ] **通知管理**
  - [ ] 通知設定介面
  - [ ] 通知歷史記錄
  - [ ] 通知偏好設定

### 3.3 使用者體驗優化
- [ ] **效能優化**
  - [ ] 圖片懶載入
  - [ ] 虛擬滾動
  - [ ] 程式碼分割
  - [ ] 快取策略優化

- [ ] **響應式設計**
  - [ ] 手機版介面優化
  - [ ] 平板版介面調整
  - [ ] 觸控操作優化

### 3.4 安全性強化
- [ ] **資料驗證**
  - [ ] 前端表單驗證強化
  - [ ] 後端資料驗證
  - [ ] SQL 注入防護
  - [ ] XSS 攻擊防護

- [ ] **存取控制**
  - [ ] API 速率限制
  - [ ] 敏感操作二次確認
  - [ ] 操作日誌記錄

## 第四階段：測試與部署

### 4.1 測試實作
- [ ] **單元測試**
  - [ ] 組件測試 (React Testing Library)
  - [ ] Hook 測試
  - [ ] 工具函數測試
  - [ ] 測試覆蓋率達 80%+

- [ ] **整合測試**
  - [ ] API 整合測試
  - [ ] 資料庫操作測試
  - [ ] 端到端測試 (Playwright)

### 4.2 部署準備
- [ ] **環境設定**
  - [ ] 生產環境變數設定
  - [ ] 建構最佳化
  - [ ] 錯誤監控設定
  - [ ] 效能監控設定

- [ ] **部署流程**
  - [ ] CI/CD 管道設定
  - [ ] 自動化測試
  - [ ] 自動化部署
  - [ ] 回滾機制

## 第五階段：維護與擴展

### 5.1 監控與分析
- [ ] **使用者行為分析**
  - [ ] 頁面瀏覽統計
  - [ ] 轉換率分析
  - [ ] 使用者路徑追蹤

- [ ] **系統監控**
  - [ ] 錯誤追蹤
  - [ ] 效能監控
  - [ ] 可用性監控

### 5.2 功能擴展
- [ ] **進階功能**
  - [ ] 多語言支援
  - [ ] 多幣別支援
  - [ ] 會員等級制度
  - [ ] 優惠券系統

- [ ] **第三方整合**
  - [ ] 金流串接
  - [ ] 物流串接
  - [ ] 簡訊通知
  - [ ] 電子郵件通知

## 任務優先級說明

### 🔴 高優先級 (立即執行)
- 資料庫設計與設定
- 基礎認證系統
- 核心商品展示功能
- 基礎購物車功能

### 🟡 中優先級 (第二階段)
- 廠商後台管理
- 訂單管理系統
- 圖片上傳功能
- 基礎統計功能

### 🟢 低優先級 (後續優化)
- 進階搜尋篩選
- 通知系統
- 效能優化
- 安全性強化

## 開發里程碑

### Milestone 1: MVP 完成 (預計 4 週)
- 基礎商品瀏覽
- 購物車功能
- 簡單訂單流程

### Milestone 2: 廠商後台 (預計 3 週)
- 廠商認證登入
- 商品管理
- 訂單管理

### Milestone 3: 功能完善 (預計 3 週)
- 進階功能
- 使用者體驗優化
- 測試與部署

### Milestone 4: 上線準備 (預計 2 週)
- 效能優化
- 安全性檢查
- 生產環境部署
