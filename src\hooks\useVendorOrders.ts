import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useVendorAuth } from './useVendorAuth';
import { toast } from 'sonner';
import type { OrderItem, VendorOrderFilter, OrderStatus } from '@/types/database';

/**
 * 廠商訂單管理 Hook
 * 提供訂單查看和狀態更新功能
 */
export const useVendorOrders = (filters?: VendorOrderFilter) => {
  const { vendor, isActiveVendor } = useVendorAuth();
  const queryClient = useQueryClient();

  // 獲取廠商訂單列表
  const {
    data: orders,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['vendor-orders', vendor?.id, filters],
    queryFn: async (): Promise<{ data: any[]; count: number }> => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      let query = supabase
        .from('order_items')
        .select(`
          *,
          orders!inner(
            id,
            customer_name,
            customer_phone,
            license_plate,
            notes,
            total_amount,
            status,
            created_at,
            updated_at
          )
        `, { count: 'exact' })
        .eq('vendor_id', vendor.id);

      // 應用篩選條件
      if (filters?.status) {
        query = query.eq('orders.status', filters.status);
      }

      if (filters?.search) {
        query = query.or(`
          product_name.ilike.%${filters.search}%,
          orders.customer_name.ilike.%${filters.search}%,
          orders.customer_phone.ilike.%${filters.search}%
        `);
      }

      if (filters?.dateFrom) {
        query = query.gte('orders.created_at', filters.dateFrom);
      }

      if (filters?.dateTo) {
        query = query.lte('orders.created_at', filters.dateTo);
      }

      // 排序
      query = query.order('created_at', { ascending: false });

      // 分頁
      const page = filters?.page || 1;
      const limit = filters?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        count: count || 0
      };
    },
    enabled: !!vendor?.id && isActiveVendor,
    staleTime: 1 * 60 * 1000, // 1 分鐘
  });

  // 更新訂單狀態
  const updateOrderStatus = useMutation({
    mutationFn: async ({ orderId, status }: { orderId: string; status: OrderStatus }) => {
      // 首先檢查這個訂單是否包含該廠商的商品
      const { data: orderItems, error: checkError } = await supabase
        .from('order_items')
        .select('id')
        .eq('order_id', orderId)
        .eq('vendor_id', vendor?.id);

      if (checkError) throw checkError;
      if (!orderItems || orderItems.length === 0) {
        throw new Error('您沒有權限更新此訂單');
      }

      // 更新訂單狀態
      const { data, error } = await supabase
        .from('orders')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (_, { status }) => {
      queryClient.invalidateQueries({ queryKey: ['vendor-orders'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success(`訂單狀態已更新為：${getStatusText(status)}`);
    },
    onError: (error: any) => {
      console.error('Update order status error:', error);
      toast.error(error.message || '訂單狀態更新失敗');
    }
  });

  // 批量更新訂單狀態
  const bulkUpdateOrderStatus = useMutation({
    mutationFn: async ({ orderIds, status }: { orderIds: string[]; status: OrderStatus }) => {
      // 檢查所有訂單是否都包含該廠商的商品
      const { data: orderItems, error: checkError } = await supabase
        .from('order_items')
        .select('order_id')
        .in('order_id', orderIds)
        .eq('vendor_id', vendor?.id);

      if (checkError) throw checkError;

      const validOrderIds = [...new Set(orderItems?.map(item => item.order_id) || [])];
      if (validOrderIds.length !== orderIds.length) {
        throw new Error('部分訂單您沒有權限更新');
      }

      // 批量更新訂單狀態
      const { error } = await supabase
        .from('orders')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .in('id', orderIds);

      if (error) throw error;
    },
    onSuccess: (_, { orderIds, status }) => {
      queryClient.invalidateQueries({ queryKey: ['vendor-orders'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success(`已將 ${orderIds.length} 個訂單狀態更新為：${getStatusText(status)}`);
    },
    onError: (error: any) => {
      console.error('Bulk update order status error:', error);
      toast.error(error.message || '批量更新失敗');
    }
  });

  return {
    // 數據
    orders: orders?.data || [],
    totalCount: orders?.count || 0,
    isLoading,
    error,
    refetch,

    // 操作
    updateOrderStatus: updateOrderStatus.mutate,
    bulkUpdateOrderStatus: bulkUpdateOrderStatus.mutate,

    // 狀態
    isUpdatingStatus: updateOrderStatus.isPending,
    isBulkUpdating: bulkUpdateOrderStatus.isPending,
  };
};

/**
 * 獲取單個訂單詳情
 */
export const useVendorOrderDetail = (orderId: string) => {
  const { vendor, isActiveVendor } = useVendorAuth();

  return useQuery({
    queryKey: ['vendor-order-detail', orderId, vendor?.id],
    queryFn: async () => {
      if (!vendor?.id || !orderId) {
        throw new Error('參數不完整');
      }

      // 獲取訂單基本資訊
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (orderError) throw orderError;

      // 獲取該廠商在此訂單中的商品項目
      const { data: orderItems, error: itemsError } = await supabase
        .from('order_items')
        .select(`
          *,
          products(
            id,
            name,
            image_url,
            unit
          )
        `)
        .eq('order_id', orderId)
        .eq('vendor_id', vendor.id);

      if (itemsError) throw itemsError;

      if (!orderItems || orderItems.length === 0) {
        throw new Error('此訂單不包含您的商品');
      }

      return {
        order,
        orderItems
      };
    },
    enabled: !!vendor?.id && !!orderId && isActiveVendor,
    staleTime: 1 * 60 * 1000, // 1 分鐘
  });
};

// 輔助函數：獲取狀態文字
function getStatusText(status: OrderStatus): string {
  const statusMap = {
    'PENDING': '待確認',
    'CONFIRMED': '已確認',
    'PREPARING': '準備中',
    'READY': '待取貨',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  };
  return statusMap[status] || status;
}
