var S=function(X,H){var z=Object.keys(X);if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(X);H&&(E=E.filter(function(x){return Object.getOwnPropertyDescriptor(X,x).enumerable})),z.push.apply(z,E)}return z},V=function(X){for(var H=1;H<arguments.length;H++){var z=arguments[H]!=null?arguments[H]:{};H%2?S(Object(z),!0).forEach(function(E){K0(X,E,z[E])}):Object.getOwnPropertyDescriptors?Object.defineProperties(X,Object.getOwnPropertyDescriptors(z)):S(Object(z)).forEach(function(E){Object.defineProperty(X,E,Object.getOwnPropertyDescriptor(z,E))})}return X},K0=function(X,H,z){if(H=M0(H),H in X)Object.defineProperty(X,H,{value:z,enumerable:!0,configurable:!0,writable:!0});else X[H]=z;return X},M0=function(X){var H=x0(X,"string");return M(H)=="symbol"?H:String(H)},x0=function(X,H){if(M(X)!="object"||!X)return X;var z=X[Symbol.toPrimitive];if(z!==void 0){var E=z.call(X,H||"default");if(M(E)!="object")return E;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(X)},M=function(X){return M=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},M(X)};(function(X){var H=Object.defineProperty,z=function I(C,U){for(var B in U)H(C,B,{get:U[B],enumerable:!0,configurable:!0,set:function J(Y){return U[B]=function(){return Y}}})},E=function I(C,U){if(U===1)return C.one;var B=U%100;if(B<=20&&B>10)return C.other;var J=B%10;if(J>=2&&J<=4)return C.twoFour;return C.other},x=function I(C,U,B){var J=E(C,U),Y=typeof J==="string"?J:J[B];return Y.replace("{{count}}",String(U))},O={lessThanXSeconds:{one:{regular:"mniej ni\u017C sekunda",past:"mniej ni\u017C sekund\u0119",future:"mniej ni\u017C sekund\u0119"},twoFour:"mniej ni\u017C {{count}} sekundy",other:"mniej ni\u017C {{count}} sekund"},xSeconds:{one:{regular:"sekunda",past:"sekund\u0119",future:"sekund\u0119"},twoFour:"{{count}} sekundy",other:"{{count}} sekund"},halfAMinute:{one:"p\xF3\u0142 minuty",twoFour:"p\xF3\u0142 minuty",other:"p\xF3\u0142 minuty"},lessThanXMinutes:{one:{regular:"mniej ni\u017C minuta",past:"mniej ni\u017C minut\u0119",future:"mniej ni\u017C minut\u0119"},twoFour:"mniej ni\u017C {{count}} minuty",other:"mniej ni\u017C {{count}} minut"},xMinutes:{one:{regular:"minuta",past:"minut\u0119",future:"minut\u0119"},twoFour:"{{count}} minuty",other:"{{count}} minut"},aboutXHours:{one:{regular:"oko\u0142o godziny",past:"oko\u0142o godziny",future:"oko\u0142o godzin\u0119"},twoFour:"oko\u0142o {{count}} godziny",other:"oko\u0142o {{count}} godzin"},xHours:{one:{regular:"godzina",past:"godzin\u0119",future:"godzin\u0119"},twoFour:"{{count}} godziny",other:"{{count}} godzin"},xDays:{one:{regular:"dzie\u0144",past:"dzie\u0144",future:"1 dzie\u0144"},twoFour:"{{count}} dni",other:"{{count}} dni"},aboutXWeeks:{one:"oko\u0142o tygodnia",twoFour:"oko\u0142o {{count}} tygodni",other:"oko\u0142o {{count}} tygodni"},xWeeks:{one:"tydzie\u0144",twoFour:"{{count}} tygodnie",other:"{{count}} tygodni"},aboutXMonths:{one:"oko\u0142o miesi\u0105c",twoFour:"oko\u0142o {{count}} miesi\u0105ce",other:"oko\u0142o {{count}} miesi\u0119cy"},xMonths:{one:"miesi\u0105c",twoFour:"{{count}} miesi\u0105ce",other:"{{count}} miesi\u0119cy"},aboutXYears:{one:"oko\u0142o rok",twoFour:"oko\u0142o {{count}} lata",other:"oko\u0142o {{count}} lat"},xYears:{one:"rok",twoFour:"{{count}} lata",other:"{{count}} lat"},overXYears:{one:"ponad rok",twoFour:"ponad {{count}} lata",other:"ponad {{count}} lat"},almostXYears:{one:"prawie rok",twoFour:"prawie {{count}} lata",other:"prawie {{count}} lat"}},$=function I(C,U,B){var J=O[C];if(!(B!==null&&B!==void 0&&B.addSuffix))return x(J,U,"regular");if(B.comparison&&B.comparison>0)return"za "+x(J,U,"future");else return x(J,U,"past")+" temu"};function R(I){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=C.width?String(C.width):I.defaultWidth,B=I.formats[U]||I.formats[I.defaultWidth];return B}}var P={full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},F={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},j={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},w={date:R({formats:P,defaultWidth:"full"}),time:R({formats:F,defaultWidth:"full"}),dateTime:R({formats:j,defaultWidth:"full"})};function v(I){var C=Object.prototype.toString.call(I);if(I instanceof Date||M(I)==="object"&&C==="[object Date]")return new I.constructor(+I);else if(typeof I==="number"||C==="[object Number]"||typeof I==="string"||C==="[object String]")return new Date(I);else return new Date(NaN)}function b(){return D}function A0(I){D=I}var D={};function L(I,C){var U,B,J,Y,Z,Q,T=b(),q=(U=(B=(J=(Y=C===null||C===void 0?void 0:C.weekStartsOn)!==null&&Y!==void 0?Y:C===null||C===void 0||(Z=C.locale)===null||Z===void 0||(Z=Z.options)===null||Z===void 0?void 0:Z.weekStartsOn)!==null&&J!==void 0?J:T.weekStartsOn)!==null&&B!==void 0?B:(Q=T.locale)===null||Q===void 0||(Q=Q.options)===null||Q===void 0?void 0:Q.weekStartsOn)!==null&&U!==void 0?U:0,G=v(I),K=G.getDay(),G0=(K<q?7:0)+K-q;return G.setDate(G.getDate()-G0),G.setHours(0,0,0,0),G}function k(I,C,U){var B=L(I,U),J=L(C,U);return+B===+J}var W=function I(C,U,B,J){var Y;if(k(U,B,J))Y=_;else if(C==="lastWeek")Y=f;else if(C==="nextWeek")Y=h;else throw new Error("Cannot determine adjectives for token ".concat(C));var Z=U.getDay(),Q=y[Z],T=Y[Q];return"'".concat(T,"' eeee 'o' p")},f={masculine:"ostatni",feminine:"ostatnia"},_={masculine:"ten",feminine:"ta"},h={masculine:"nast\u0119pny",feminine:"nast\u0119pna"},y={0:"feminine",1:"masculine",2:"masculine",3:"feminine",4:"masculine",5:"masculine",6:"feminine"},m={lastWeek:W,yesterday:"'wczoraj o' p",today:"'dzisiaj o' p",tomorrow:"'jutro o' p",nextWeek:W,other:"P"},c=function I(C,U,B,J){var Y=m[C];if(typeof Y==="function")return Y(C,U,B,J);return Y};function A(I){return function(C,U){var B=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",J;if(B==="formatting"&&I.formattingValues){var Y=I.defaultFormattingWidth||I.defaultWidth,Z=U!==null&&U!==void 0&&U.width?String(U.width):Y;J=I.formattingValues[Z]||I.formattingValues[Y]}else{var Q=I.defaultWidth,T=U!==null&&U!==void 0&&U.width?String(U.width):I.defaultWidth;J=I.values[T]||I.values[Q]}var q=I.argumentCallback?I.argumentCallback(C):C;return J[q]}}var g={narrow:["p.n.e.","n.e."],abbreviated:["p.n.e.","n.e."],wide:["przed nasz\u0105 er\u0105","naszej ery"]},u={narrow:["1","2","3","4"],abbreviated:["I kw.","II kw.","III kw.","IV kw."],wide:["I kwarta\u0142","II kwarta\u0142","III kwarta\u0142","IV kwarta\u0142"]},p={narrow:["S","L","M","K","M","C","L","S","W","P","L","G"],abbreviated:["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","pa\u017A","lis","gru"],wide:["stycze\u0144","luty","marzec","kwiecie\u0144","maj","czerwiec","lipiec","sierpie\u0144","wrzesie\u0144","pa\u017Adziernik","listopad","grudzie\u0144"]},l={narrow:["s","l","m","k","m","c","l","s","w","p","l","g"],abbreviated:["sty","lut","mar","kwi","maj","cze","lip","sie","wrz","pa\u017A","lis","gru"],wide:["stycznia","lutego","marca","kwietnia","maja","czerwca","lipca","sierpnia","wrze\u015Bnia","pa\u017Adziernika","listopada","grudnia"]},d={narrow:["N","P","W","\u015A","C","P","S"],short:["nie","pon","wto","\u015Bro","czw","pi\u0105","sob"],abbreviated:["niedz.","pon.","wt.","\u015Br.","czw.","pt.","sob."],wide:["niedziela","poniedzia\u0142ek","wtorek","\u015Broda","czwartek","pi\u0105tek","sobota"]},i={narrow:["n","p","w","\u015B","c","p","s"],short:["nie","pon","wto","\u015Bro","czw","pi\u0105","sob"],abbreviated:["niedz.","pon.","wt.","\u015Br.","czw.","pt.","sob."],wide:["niedziela","poniedzia\u0142ek","wtorek","\u015Broda","czwartek","pi\u0105tek","sobota"]},n={narrow:{am:"a",pm:"p",midnight:"p\xF3\u0142n.",noon:"po\u0142",morning:"rano",afternoon:"popo\u0142.",evening:"wiecz.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"p\xF3\u0142noc",noon:"po\u0142udnie",morning:"rano",afternoon:"popo\u0142udnie",evening:"wiecz\xF3r",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"p\xF3\u0142noc",noon:"po\u0142udnie",morning:"rano",afternoon:"popo\u0142udnie",evening:"wiecz\xF3r",night:"noc"}},r={narrow:{am:"a",pm:"p",midnight:"o p\xF3\u0142n.",noon:"w po\u0142.",morning:"rano",afternoon:"po po\u0142.",evening:"wiecz.",night:"w nocy"},abbreviated:{am:"AM",pm:"PM",midnight:"o p\xF3\u0142nocy",noon:"w po\u0142udnie",morning:"rano",afternoon:"po po\u0142udniu",evening:"wieczorem",night:"w nocy"},wide:{am:"AM",pm:"PM",midnight:"o p\xF3\u0142nocy",noon:"w po\u0142udnie",morning:"rano",afternoon:"po po\u0142udniu",evening:"wieczorem",night:"w nocy"}},s=function I(C,U){return String(C)},o={ordinalNumber:s,era:A({values:g,defaultWidth:"wide"}),quarter:A({values:u,defaultWidth:"wide",argumentCallback:function I(C){return C-1}}),month:A({values:p,defaultWidth:"wide",formattingValues:l,defaultFormattingWidth:"wide"}),day:A({values:d,defaultWidth:"wide",formattingValues:i,defaultFormattingWidth:"wide"}),dayPeriod:A({values:n,defaultWidth:"wide",formattingValues:r,defaultFormattingWidth:"wide"})};function N(I){return function(C){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.width,J=B&&I.matchPatterns[B]||I.matchPatterns[I.defaultMatchWidth],Y=C.match(J);if(!Y)return null;var Z=Y[0],Q=B&&I.parsePatterns[B]||I.parsePatterns[I.defaultParseWidth],T=Array.isArray(Q)?e(Q,function(K){return K.test(Z)}):a(Q,function(K){return K.test(Z)}),q;q=I.valueCallback?I.valueCallback(T):T,q=U.valueCallback?U.valueCallback(q):q;var G=C.slice(Z.length);return{value:q,rest:G}}}var a=function I(C,U){for(var B in C)if(Object.prototype.hasOwnProperty.call(C,B)&&U(C[B]))return B;return},e=function I(C,U){for(var B=0;B<C.length;B++)if(U(C[B]))return B;return};function t(I){return function(C){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.match(I.matchPattern);if(!B)return null;var J=B[0],Y=C.match(I.parsePattern);if(!Y)return null;var Z=I.valueCallback?I.valueCallback(Y[0]):Y[0];Z=U.valueCallback?U.valueCallback(Z):Z;var Q=C.slice(J.length);return{value:Z,rest:Q}}}var C0=/^(\d+)?/i,I0=/\d+/i,U0={narrow:/^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,abbreviated:/^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,wide:/^(przed\s*nasz(ą|a)\s*er(ą|a)|naszej\s*ery)/i},B0={any:[/^p/i,/^n/i]},H0={narrow:/^[1234]/i,abbreviated:/^(I|II|III|IV)\s*kw\.?/i,wide:/^(I|II|III|IV)\s*kwarta(ł|l)/i},J0={narrow:[/1/i,/2/i,/3/i,/4/i],any:[/^I kw/i,/^II kw/i,/^III kw/i,/^IV kw/i]},X0={narrow:/^[slmkcwpg]/i,abbreviated:/^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,wide:/^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i},Y0={narrow:[/^s/i,/^l/i,/^m/i,/^k/i,/^m/i,/^c/i,/^l/i,/^s/i,/^w/i,/^p/i,/^l/i,/^g/i],any:[/^st/i,/^lu/i,/^mar/i,/^k/i,/^maj/i,/^c/i,/^lip/i,/^si/i,/^w/i,/^p/i,/^lis/i,/^g/i]},Z0={narrow:/^[npwścs]/i,short:/^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,abbreviated:/^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\.?/i,wide:/^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i},z0={narrow:[/^n/i,/^p/i,/^w/i,/^ś/i,/^c/i,/^p/i,/^s/i],abbreviated:[/^n/i,/^po/i,/^w/i,/^(ś|s)r/i,/^c/i,/^pt/i,/^so/i],any:[/^n/i,/^po/i,/^w/i,/^(ś|s)r/i,/^c/i,/^pi/i,/^so/i]},Q0={narrow:/^(^a$|^p$|pó(ł|l)n\.?|o\s*pó(ł|l)n\.?|po(ł|l)\.?|w\s*po(ł|l)\.?|po\s*po(ł|l)\.?|rano|wiecz\.?|noc|w\s*nocy)/i,any:/^(am|pm|pó(ł|l)noc|o\s*pó(ł|l)nocy|po(ł|l)udnie|w\s*po(ł|l)udnie|popo(ł|l)udnie|po\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\s*nocy)/i},E0={narrow:{am:/^a$/i,pm:/^p$/i,midnight:/pó(ł|l)n/i,noon:/po(ł|l)/i,morning:/rano/i,afternoon:/po\s*po(ł|l)/i,evening:/wiecz/i,night:/noc/i},any:{am:/^am/i,pm:/^pm/i,midnight:/pó(ł|l)n/i,noon:/po(ł|l)/i,morning:/rano/i,afternoon:/po\s*po(ł|l)/i,evening:/wiecz/i,night:/noc/i}},q0={ordinalNumber:t({matchPattern:C0,parsePattern:I0,valueCallback:function I(C){return parseInt(C,10)}}),era:N({matchPatterns:U0,defaultMatchWidth:"wide",parsePatterns:B0,defaultParseWidth:"any"}),quarter:N({matchPatterns:H0,defaultMatchWidth:"wide",parsePatterns:J0,defaultParseWidth:"any",valueCallback:function I(C){return C+1}}),month:N({matchPatterns:X0,defaultMatchWidth:"wide",parsePatterns:Y0,defaultParseWidth:"any"}),day:N({matchPatterns:Z0,defaultMatchWidth:"wide",parsePatterns:z0,defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:Q0,defaultMatchWidth:"any",parsePatterns:E0,defaultParseWidth:"any"})},T0={code:"pl",formatDistance:$,formatLong:w,formatRelative:c,localize:o,match:q0,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=V(V({},window.dateFns),{},{locale:V(V({},(X=window.dateFns)===null||X===void 0?void 0:X.locale),{},{pl:T0})})})();

//# debugId=3C4090FE40B393CC64756e2164756e21
