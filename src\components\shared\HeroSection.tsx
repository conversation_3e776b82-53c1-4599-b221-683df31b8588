
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight, TrendingUp, Users, Truck } from 'lucide-react';

const HeroSection = () => {
  return (
    <section className="relative bg-gradient-to-br from-market-green-50 via-white to-market-green-50 py-20">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left content */}
          <div className="space-y-8 animate-fade-in">
            <div className="space-y-4">
              <div className="inline-flex items-center px-4 py-2 bg-market-green-100 text-market-green-700 rounded-full text-sm font-medium">
                🌱 台灣最大果菜批發平台
              </div>
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                連接<span className="text-market-green-600">農場</span>
                <br />
                與<span className="text-market-green-600">餐桌</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                直接與優質供應商合作，獲得最新鮮的農產品。
                透明價格、即時配送、品質保證。
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="gradient-market text-white px-8">
                開始採購
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button variant="outline" size="lg" className="border-market-green-300 text-market-green-700 hover:bg-market-green-50">
                成為供應商
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-market-green-600">500+</div>
                <div className="text-sm text-gray-600">優質供應商</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-market-green-600">10K+</div>
                <div className="text-sm text-gray-600">種類商品</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-market-green-600">98%</div>
                <div className="text-sm text-gray-600">客戶滿意度</div>
              </div>
            </div>
          </div>

          {/* Right content */}
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                  <TrendingUp className="w-8 h-8 text-market-green-500 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">即時行情</h3>
                  <p className="text-sm text-gray-600">掌握最新市場價格變化</p>
                  <div className="mt-4 text-2xl font-bold text-market-green-600">+12%</div>
                </div>
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                  <Users className="w-8 h-8 text-market-orange-500 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">供應商網路</h3>
                  <p className="text-sm text-gray-600">全台最大供應商社群</p>
                  <div className="mt-4 text-2xl font-bold text-market-orange-600">500+</div>
                </div>
              </div>
              <div className="space-y-4 pt-8">
                <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                  <Truck className="w-8 h-8 text-blue-500 mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-2">快速配送</h3>
                  <p className="text-sm text-gray-600">當日下單，隔日到貨</p>
                  <div className="mt-4 text-2xl font-bold text-blue-600">24h</div>
                </div>
                <div className="bg-gradient-to-br from-market-green-500 to-market-green-600 rounded-2xl p-6 text-white">
                  <div className="text-3xl mb-2">🥬</div>
                  <h3 className="font-semibold mb-2">新鮮直送</h3>
                  <p className="text-sm text-green-100">從產地到餐桌，保證新鮮</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
