import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useVendorAuth } from '@/hooks/useVendorAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertTriangle, Clock, Ban } from 'lucide-react';
import { Link } from 'react-router-dom';

interface VendorProtectedRouteProps {
  children: React.ReactNode;
  requireActive?: boolean; // 是否需要廠商狀態為 ACTIVE
}

/**
 * 廠商路由保護組件
 * 確保只有廠商用戶可以訪問特定路由
 */
const VendorProtectedRoute: React.FC<VendorProtectedRouteProps> = ({
  children,
  requireActive = true
}) => {
  const location = useLocation();
  const {
    vendorData,
    isLoading,
    isVendor,
    isActiveVendor,
    isPendingVendor,
    isSuspendedVendor
  } = useVendorAuth();

  // 載入中狀態
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-market-green-500 mb-4" />
            <p className="text-gray-600">驗證廠商身份中...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 未登入，重定向到登入頁面
  if (!vendorData) {
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // 不是廠商用戶
  if (!isVendor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">存取被拒絕</CardTitle>
            <CardDescription>
              此頁面僅限廠商用戶訪問
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600 text-center">
              您目前是一般用戶，如需成為廠商請先註冊廠商帳戶。
            </p>
            <div className="flex flex-col space-y-2">
              <Button asChild className="w-full">
                <Link to="/auth/vendor-register">
                  申請成為廠商
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link to="/">
                  返回首頁
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 廠商帳戶被暫停
  if (isSuspendedVendor) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Ban className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">帳戶已暫停</CardTitle>
            <CardDescription>
              您的廠商帳戶目前已被暫停使用
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600 text-center">
              如有疑問，請聯繫客服人員了解詳情。
            </p>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">聯繫方式：</h4>
              <p className="text-sm text-gray-600">📞 客服專線: 0800-123-456</p>
              <p className="text-sm text-gray-600">📧 客服信箱: <EMAIL></p>
            </div>
            <Button variant="outline" asChild className="w-full">
              <Link to="/">
                返回首頁
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 廠商帳戶審核中，但需要 ACTIVE 狀態
  if (isPendingVendor && requireActive) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle className="text-xl">帳戶審核中</CardTitle>
            <CardDescription>
              您的廠商帳戶正在審核中
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600 text-center">
              我們正在審核您的廠商申請，通常需要 1-3 個工作天。
              審核完成後您將收到電子郵件通知。
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">廠商資訊：</h4>
              <p className="text-sm text-gray-600">廠商名稱: {vendorData.vendor?.name}</p>
              <p className="text-sm text-gray-600">聯絡電話: {vendorData.vendor?.phone}</p>
              <p className="text-sm text-gray-600">攤位位置: {vendorData.vendor?.location}</p>
            </div>
            <Button variant="outline" asChild className="w-full">
              <Link to="/">
                返回首頁
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 通過所有檢查，渲染子組件
  return <>{children}</>;
};

export default VendorProtectedRoute;
