import React from 'react';
import { useNavigate } from 'react-router-dom';
import ProductForm from '@/components/vendor/ProductForm';
import { useVendorProducts } from '@/hooks/useVendorProducts';

/**
 * 新增商品頁面
 */
const VendorProductNew: React.FC = () => {
  const navigate = useNavigate();
  const { createProduct, isCreating } = useVendorProducts();

  const handleSubmit = (data: any) => {
    createProduct(data, {
      onSuccess: () => {
        navigate('/vendor/products');
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">新增商品</h1>
        <p className="text-gray-600 mt-1">
          填寫商品資訊來新增商品到您的商店
        </p>
      </div>

      {/* 商品表單 */}
      <ProductForm
        mode="create"
        onSubmit={handleSubmit}
        isLoading={isCreating}
      />
    </div>
  );
};

export default VendorProductNew;
