
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface CategoryNavigationProps {
  categories: Array<{
    id: string;
    name: string;
    icon: string;
    count: number;
  }>;
  selectedCategory?: string;
  onCategorySelect: (categoryId: string | undefined) => void;
}

const CategoryNavigation: React.FC<CategoryNavigationProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
}) => {
  return (
    <div className="bg-white border-b sticky top-0 z-40">
      <div className="container mx-auto px-4">
        <div className="flex items-center space-x-2 py-4 overflow-x-auto scrollbar-hide">
          <Button
            variant={!selectedCategory ? "default" : "outline"}
            size="sm"
            onClick={() => onCategorySelect(undefined)}
            className={`flex-shrink-0 ${
              !selectedCategory 
                ? 'bg-market-green-500 text-white' 
                : 'hover:bg-market-green-50'
            }`}
          >
            <span className="mr-2">🏪</span>
            全部商品
          </Button>
          
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => onCategorySelect(category.id)}
              className={`flex-shrink-0 ${
                selectedCategory === category.id
                  ? 'bg-market-green-500 text-white'
                  : 'hover:bg-market-green-50'
              }`}
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
              <Badge 
                variant="secondary" 
                className="ml-2 text-xs"
              >
                {category.count}
              </Badge>
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CategoryNavigation;
