
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Leaf,
  ShoppingCart,
  User,
  Search,
  Menu,
  Bell,
  ChevronDown,
  LogOut,
  Settings,
  Package
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/hooks/useAuth';
import { useCartStore } from '@/stores/cartStore';
import { useVendorAuth } from '@/hooks/useVendorAuth';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, isAuthenticated, signOut } = useAuth();
  const { totalItems } = useCartStore();
  const { isVendor, canAccessDashboard } = useVendorAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        {/* Top bar */}
        <div className="flex items-center justify-between py-2 text-sm text-gray-600 border-b">
          <div className="flex items-center space-x-4">
            <span>📞 客服專線: 0800-123-456</span>
            <span>📧 <EMAIL></span>
          </div>
          <div className="flex items-center space-x-4">
            <span>營業時間: 週一至週五 6:00-18:00</span>
          </div>
        </div>

        {/* Main header */}
        <div className="flex items-center justify-between py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-market-green-500 to-market-green-600 rounded-xl flex items-center justify-center">
              <Leaf className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">果菜市場</h1>
              <p className="text-xs text-gray-500">Fresh Market Platform</p>
            </div>
          </Link>

          {/* Search bar */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="搜尋農產品、供應商..."
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-market-green-500 focus:border-transparent"
              />
              <Button 
                size="sm" 
                className="absolute right-2 top-1/2 transform -translate-y-1/2 gradient-market text-white"
              >
                搜尋
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-1">
                  <span>產品分類</span>
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48">
                <DropdownMenuItem>🥬 葉菜類</DropdownMenuItem>
                <DropdownMenuItem>🥕 根莖類</DropdownMenuItem>
                <DropdownMenuItem>🍅 果菜類</DropdownMenuItem>
                <DropdownMenuItem>🍄 菇類</DropdownMenuItem>
                <DropdownMenuItem>🌶️ 辛香料</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Link to="/market" className="text-gray-700 hover:text-market-green-600 transition-colors">
              市場行情
            </Link>
            <Link to="/vendors" className="text-gray-700 hover:text-market-green-600 transition-colors">
              供應商
            </Link>
            <Link to="/about" className="text-gray-700 hover:text-market-green-600 transition-colors">
              關於我們
            </Link>
          </nav>

          {/* User actions */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-5 h-5" />
              <Badge className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                3
              </Badge>
            </Button>

            {/* Cart */}
            <Button variant="ghost" size="sm" className="relative">
              <ShoppingCart className="w-5 h-5" />
              {totalItems > 0 && (
                <Badge className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-market-green-500 text-white text-xs flex items-center justify-center">
                  {totalItems}
                </Badge>
              )}
            </Button>

            {/* User menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span className="hidden md:block">
                    {isAuthenticated ? '會員中心' : '登入/註冊'}
                  </span>
                  <ChevronDown className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48">
                {isAuthenticated ? (
                  <>
                    <DropdownMenuItem className="font-medium">
                      {user?.email}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {isVendor && canAccessDashboard && (
                      <DropdownMenuItem onClick={() => navigate('/vendor')}>
                        <Package className="mr-2 h-4 w-4" />
                        廠商後台
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => navigate('/orders')}>
                      <Settings className="mr-2 h-4 w-4" />
                      訂單管理
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/profile')}>
                      <User className="mr-2 h-4 w-4" />
                      個人設定
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="mr-2 h-4 w-4" />
                      登出
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem onClick={() => navigate('/auth/login')}>
                      登入
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/auth/register')}>
                      一般用戶註冊
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/auth/vendor-register')}>
                      廠商註冊
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile menu */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Mobile search */}
        <div className="md:hidden pb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜尋農產品..."
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-market-green-500"
            />
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t">
            <nav className="flex flex-col space-y-2">
              <Link to="/products" className="text-gray-700 hover:text-market-green-600 py-2">產品分類</Link>
              <Link to="/market" className="text-gray-700 hover:text-market-green-600 py-2">市場行情</Link>
              <Link to="/vendors" className="text-gray-700 hover:text-market-green-600 py-2">供應商</Link>
              <Link to="/about" className="text-gray-700 hover:text-market-green-600 py-2">關於我們</Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
