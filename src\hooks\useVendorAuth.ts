import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import type { Vendor, VendorUser } from '@/types/database';

/**
 * 廠商認證相關 Hook
 * 用於檢查當前用戶是否為廠商，並獲取廠商資訊
 */
export const useVendorAuth = () => {
  const { user, isAuthenticated } = useAuth();

  // 獲取廠商資訊
  const {
    data: vendorData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['vendor-auth', user?.id],
    queryFn: async (): Promise<VendorUser | null> => {
      if (!user?.id) return null;

      // 查詢廠商資料
      const { data: vendor, error: vendorError } = await supabase
        .from('vendors')
        .select('*')
        .eq('id', user.id)
        .single();

      if (vendorError) {
        // 如果找不到廠商資料，表示是一般用戶
        if (vendorError.code === 'PGRST116') {
          return {
            id: user.id,
            email: user.email || '',
            role: 'CUSTOMER'
          };
        }
        throw vendorError;
      }

      return {
        id: user.id,
        email: user.email || '',
        vendor: vendor as Vendor,
        role: 'VENDOR'
      };
    },
    enabled: !!user?.id && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 分鐘
    retry: 1
  });

  // 計算衍生狀態
  const isVendor = vendorData?.role === 'VENDOR';
  const isActiveVendor = isVendor && vendorData?.vendor?.status === 'ACTIVE';
  const isPendingVendor = isVendor && vendorData?.vendor?.status === 'PENDING';
  const isSuspendedVendor = isVendor && vendorData?.vendor?.status === 'SUSPENDED';

  return {
    // 基本資訊
    vendorData,
    vendor: vendorData?.vendor,
    isLoading,
    error,
    refetch,

    // 狀態檢查
    isVendor,
    isActiveVendor,
    isPendingVendor,
    isSuspendedVendor,
    
    // 權限檢查
    canManageProducts: isActiveVendor,
    canManageOrders: isActiveVendor,
    canAccessDashboard: isVendor, // 即使是 pending 也可以看到儀表板
  };
};

/**
 * 廠商狀態檢查 Hook
 * 用於在組件中快速檢查廠商狀態
 */
export const useVendorStatus = () => {
  const { vendorData, isLoading } = useVendorAuth();

  const getStatusMessage = () => {
    if (isLoading) return '載入中...';
    
    if (!vendorData?.vendor) {
      return '您不是廠商用戶';
    }

    switch (vendorData.vendor.status) {
      case 'ACTIVE':
        return '廠商帳戶已啟用';
      case 'PENDING':
        return '廠商帳戶審核中，請耐心等候';
      case 'SUSPENDED':
        return '廠商帳戶已暫停，請聯繫客服';
      default:
        return '未知狀態';
    }
  };

  const getStatusColor = () => {
    if (!vendorData?.vendor) return 'gray';
    
    switch (vendorData.vendor.status) {
      case 'ACTIVE':
        return 'green';
      case 'PENDING':
        return 'yellow';
      case 'SUSPENDED':
        return 'red';
      default:
        return 'gray';
    }
  };

  return {
    status: vendorData?.vendor?.status,
    message: getStatusMessage(),
    color: getStatusColor(),
    isLoading
  };
};
