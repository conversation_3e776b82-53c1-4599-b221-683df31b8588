
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface MarketItem {
  name: string;
  price: number;
  change: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
}

const MarketStats = () => {
  const marketData: MarketItem[] = [
    { name: '高麗菜', price: 25, change: 5.2, unit: '公斤', trend: 'up' },
    { name: '白蘿蔔', price: 18, change: -2.1, unit: '公斤', trend: 'down' },
    { name: '紅蘿蔔', price: 32, change: 0, unit: '公斤', trend: 'stable' },
    { name: '花椰菜', price: 45, change: 8.7, unit: '公斤', trend: 'up' },
    { name: '青椒', price: 65, change: -3.4, unit: '公斤', trend: 'down' },
    { name: '茄子', price: 28, change: 1.2, unit: '公斤', trend: 'up' },
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">今日市場行情</h2>
          <p className="text-lg text-gray-600">即時更新的農產品價格資訊</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {marketData.map((item, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow bg-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <span className="text-lg">{item.name}</span>
                  {getTrendIcon(item.trend)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold text-gray-900">
                      ${item.price}
                    </div>
                    <div className="text-sm text-gray-500">每{item.unit}</div>
                  </div>
                  <div className={`text-right ${getTrendColor(item.trend)}`}>
                    <div className="text-lg font-semibold">
                      {item.change > 0 ? '+' : ''}{item.change}%
                    </div>
                    <div className="text-xs">較昨日</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-sm text-gray-500 mb-4">
            更新時間：{new Date().toLocaleString('zh-TW')}
          </p>
          <button className="text-market-green-600 hover:text-market-green-700 font-medium">
            查看完整行情表 →
          </button>
        </div>
      </div>
    </section>
  );
};

export default MarketStats;
