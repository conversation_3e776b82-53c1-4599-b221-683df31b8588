import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link, useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Loader2, Store } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';

import { useAuth } from '@/hooks/useAuth';
import { vendorRegisterSchema, type VendorRegisterFormData } from '@/lib/validations/auth';

const VendorRegisterForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { signUp, loading } = useAuth();
  const navigate = useNavigate();

  const form = useForm<VendorRegisterFormData>({
    resolver: zodResolver(vendorRegisterSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      name: '',
      phone: '',
      location: '',
    },
  });

  const onSubmit = async (data: VendorRegisterFormData) => {
    const vendorData = {
      name: data.name,
      phone: data.phone,
      location: data.location,
      email: data.email,
    };

    const { data: result, error } = await signUp(data.email, data.password, vendorData);
    
    if (result && !error) {
      navigate('/auth/login');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-lg">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-2">
            <Store className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-center">廠商註冊</CardTitle>
          <CardDescription className="text-center">
            註冊成為果菜市場的供應商
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertDescription>
              註冊後需要等待管理員審核，審核通過後您將收到通知郵件。
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* 帳戶資訊區塊 */}
              <div>
                <h3 className="text-lg font-medium mb-3">帳戶資訊</h3>
                
                {/* 電子郵件欄位 */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>電子郵件</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="請輸入您的電子郵件"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 密碼欄位 */}
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>密碼</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            placeholder="請輸入密碼"
                            {...field}
                            disabled={loading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            disabled={loading}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 確認密碼欄位 */}
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>確認密碼</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showConfirmPassword ? 'text' : 'password'}
                            placeholder="請再次輸入密碼"
                            {...field}
                            disabled={loading}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            disabled={loading}
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* 廠商資訊區塊 */}
              <div>
                <h3 className="text-lg font-medium mb-3">廠商資訊</h3>
                
                {/* 廠商名稱欄位 */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>廠商名稱</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="請輸入廠商名稱"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 聯絡電話欄位 */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>聯絡電話</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="例如：02-12345678 或 0912345678"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 攤位位置欄位 */}
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem className="mb-4">
                      <FormLabel>攤位位置</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="例如：A區第3排第5號"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 註冊按鈕 */}
              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    註冊中...
                  </>
                ) : (
                  '提交廠商註冊申請'
                )}
              </Button>
            </form>
          </Form>

          {/* 分隔線 */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">已有帳戶？</span>
              </div>
            </div>
          </div>

          {/* 登入連結 */}
          <div className="mt-6">
            <Link to="/auth/login">
              <Button variant="outline" className="w-full">
                立即登入
              </Button>
            </Link>
          </div>

          {/* 返回首頁 */}
          <div className="mt-4 text-center">
            <Link 
              to="/" 
              className="text-sm text-gray-600 hover:text-gray-900 underline"
            >
              返回首頁
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorRegisterForm;
