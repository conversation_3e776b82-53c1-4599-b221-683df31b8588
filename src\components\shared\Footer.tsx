
import React from 'react';
import { Link } from 'react-router-dom';
import { Leaf, Phone, Mail, MapPin, Clock } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-market-green-500 to-market-green-600 rounded-xl flex items-center justify-center">
                <Leaf className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">果菜市場</h3>
                <p className="text-xs text-gray-400">Fresh Market Platform</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              台灣最大的果菜批發平台，連接優質農產品供應商與採購商，
              提供透明價格、即時配送、品質保證的專業服務。
            </p>
            <div className="flex space-x-4">
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-market-green-600 transition-colors cursor-pointer">
                📘
              </div>
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-market-green-600 transition-colors cursor-pointer">
                📷
              </div>
              <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center hover:bg-market-green-600 transition-colors cursor-pointer">
                🐦
              </div>
            </div>
          </div>

          {/* Quick links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">快速連結</h4>
            <nav className="flex flex-col space-y-2">
              <Link to="/about" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                關於我們
              </Link>
              <Link to="/products" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                產品分類
              </Link>
              <Link to="/vendors" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                供應商夥伴
              </Link>
              <Link to="/market" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                市場行情
              </Link>
              <Link to="/contact" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                聯絡我們
              </Link>
            </nav>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">服務項目</h4>
            <nav className="flex flex-col space-y-2">
              <Link to="/wholesale" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                批發採購
              </Link>
              <Link to="/retail" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                零售配送
              </Link>
              <Link to="/logistics" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                物流服務
              </Link>
              <Link to="/quality" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                品質檢驗
              </Link>
              <Link to="/consulting" className="text-gray-300 hover:text-market-green-400 transition-colors text-sm">
                諮詢服務
              </Link>
            </nav>
          </div>

          {/* Contact info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">聯絡資訊</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-market-green-400" />
                <span className="text-sm text-gray-300">0800-123-456</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-market-green-400" />
                <span className="text-sm text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-market-green-400" />
                <span className="text-sm text-gray-300">台北市中正區果菜路123號</span>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-market-green-400" />
                <span className="text-sm text-gray-300">週一至週五 6:00-18:00</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">
              © 2024 果菜市場電商平台. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm text-gray-400">
              <Link to="/privacy" className="hover:text-market-green-400 transition-colors">
                隱私權政策
              </Link>
              <Link to="/terms" className="hover:text-market-green-400 transition-colors">
                服務條款
              </Link>
              <Link to="/support" className="hover:text-market-green-400 transition-colors">
                客戶支援
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
