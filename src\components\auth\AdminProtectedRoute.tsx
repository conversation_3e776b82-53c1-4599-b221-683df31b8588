import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

// 管理員 Email 列表 (在實際應用中應該從資料庫或環境變數讀取)
const ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  // 可以添加更多管理員 Email
];

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth();

  // 載入中顯示載入畫面
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">驗證管理員權限中...</p>
        </div>
      </div>
    );
  }

  // 未登入則導向登入頁面
  if (!user) {
    return <Navigate to="/auth/login" replace />;
  }

  // 檢查是否為管理員
  const isAdmin = ADMIN_EMAILS.includes(user.email || '');
  
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="text-red-500 text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">存取被拒絕</h1>
          <p className="text-gray-600 mb-6">
            您沒有管理員權限，無法存取此頁面。
          </p>
          <div className="space-y-3">
            <a 
              href="/"
              className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              返回首頁
            </a>
            <a 
              href="/vendor"
              className="block w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              前往廠商後台
            </a>
          </div>
        </div>
      </div>
    );
  }

  // 管理員權限驗證通過，顯示子組件
  return <>{children}</>;
};

export default AdminProtectedRoute;
