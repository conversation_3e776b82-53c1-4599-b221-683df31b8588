# 果菜市場電商系統 - Lovable.ai 開發提示詞

## 第一階段：基礎架構設定

### 提示詞 1：項目初始化
```
Create a full-stack fruit and vegetable wholesale market e-commerce platform with:

**Frontend (React + TypeScript):**
- Next.js 14 with App Router
- Tailwind CSS for styling
- Shadcn/ui components
- React Hook Form for forms
- Zustand for state management
- React Query for API calls

**Backend (Node.js):**
- Express.js with TypeScript
- Prisma ORM (configured for PostgreSQL but adaptable)
- JWT authentication
- Multer for file uploads
- CORS enabled
- Environment variables setup

**Project Structure:**
```
fruit-market/
├── frontend/
│   ├── app/
│   │   ├── (auth)/
│   │   ├── (vendor)/
│   │   ├── (customer)/
│   │   └── api/
│   ├── components/
│   │   ├── ui/
│   │   ├── forms/
│   │   ├── layout/
│   │   └── shared/
│   └── lib/
└── backend/
    ├── src/
    │   ├── routes/
    │   ├── middleware/
    │   ├── controllers/
    │   ├── models/
    │   └── utils/
    └── prisma/
```

Setup package.json scripts for both frontend and backend development.
```

## 第二階段：數據模型設計

### 提示詞 2：Supabase 數據庫設計
```
Setup Supabase database tables for the fruit market platform using SQL:

**Vendors Table:**
```sql
CREATE TABLE vendors (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  phone TEXT NOT NULL,
  location TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACTIVE', 'SUSPENDED')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Products Table:**
```sql
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  stock INTEGER NOT NULL DEFAULT 0,
  unit TEXT NOT NULL CHECK (unit IN ('KG', 'G', 'PIECE', 'BOX', 'JIN')),
  image_url TEXT,
  category TEXT NOT NULL,
  status TEXT DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Orders Table:**
```sql
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_name TEXT NOT NULL,
  customer_phone TEXT NOT NULL,
  license_plate TEXT,
  notes TEXT,
  total_amount DECIMAL(10,2) NOT NULL,
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'PREPARING', 'READY', 'COMPLETED', 'CANCELLED')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Order Items Table:**
```sql
CREATE TABLE order_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id),
  vendor_id UUID REFERENCES vendors(id),
  product_name TEXT NOT NULL,
  quantity DECIMAL(10,2) NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Categories Table:**
```sql
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Setup Row Level Security (RLS):**
```sql
-- Enable RLS
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Vendor policies
CREATE POLICY "Vendors can view their own data" ON vendors
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Vendors can update their own data" ON vendors
  FOR UPDATE USING (auth.uid() = id);

-- Product policies
CREATE POLICY "Anyone can view active products" ON products
  FOR SELECT USING (status = 'ACTIVE');

CREATE POLICY "Vendors can manage their own products" ON products
  FOR ALL USING (vendor_id = auth.uid());

-- Order policies
CREATE POLICY "Vendors can view orders containing their products" ON orders
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM order_items 
      WHERE order_items.order_id = orders.id 
      AND order_items.vendor_id = auth.uid()
    )
  );
```

**Setup Functions and Triggers:**
```sql
-- Update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_vendors_updated_at
  BEFORE UPDATE ON vendors
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_orders_updated_at
  BEFORE UPDATE ON orders
  FOR EACH ROW EXECUTE FUNCTION update_updated_at();
```

Also setup Supabase Auth for vendor authentication and configure storage buckets for product images.
```

## 第三階段：後端 API 開發

### 提示詞 3：Supabase API 整合與邏輯
```
Setup Supabase client and create custom React hooks for data operations:

**Supabase Client Setup:**
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types
export interface Vendor {
  id: string
  name: string
  phone: string
  location: string
  email: string
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED'
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  vendor_id: string
  name: string
  description?: string
  price: number
  stock: number
  unit: 'KG' | 'G' | 'PIECE' | 'BOX' | 'JIN'
  image_url?: string
  category: string
  status: 'ACTIVE' | 'INACTIVE'
  created_at: string
  updated_at: string
  vendor?: Vendor
}

export interface Order {
  id: string
  customer_name: string
  customer_phone: string
  license_plate?: string
  notes?: string
  total_amount: number
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'COMPLETED' | 'CANCELLED'
  created_at: string
  updated_at: string
  order_items?: OrderItem[]
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  vendor_id: string
  product_name: string
  quantity: number
  unit_price: number
  subtotal: number
  created_at: string
}
```

**Custom Hooks for Data Operations:**

**useAuth Hook:**
```typescript
export const useAuth = () => {
  const signUp = async (email: string, password: string, vendorData: any) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    
    if (data.user && !error) {
      // Insert vendor data
      await supabase.from('vendors').insert({
        id: data.user.id,
        ...vendorData
      })
    }
    
    return { data, error }
  }
  
  const signIn = async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password })
  }
  
  const signOut = async () => {
    return await supabase.auth.signOut()
  }
  
  return { signUp, signIn, signOut }
}
```

**useProducts Hook:**
```typescript
export const useProducts = () => {
  const getProducts = async (filters?: {
    vendor_id?: string
    category?: string
    search?: string
    status?: string
  }) => {
    let query = supabase
      .from('products')
      .select(`
        *,
        vendor:vendors(name, location)
      `)
      .eq('status', 'ACTIVE')
    
    if (filters?.vendor_id) {
      query = query.eq('vendor_id', filters.vendor_id)
    }
    
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }
    
    if (filters?.search) {
      query = query.ilike('name', `%${filters.search}%`)
    }
    
    return await query
  }
  
  const createProduct = async (productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    return await supabase.from('products').insert(productData)
  }
  
  const updateProduct = async (id: string, updates: Partial<Product>) => {
    return await supabase.from('products').update(updates).eq('id', id)
  }
  
  const deleteProduct = async (id: string) => {
    return await supabase.from('products').delete().eq('id', id)
  }
  
  return { getProducts, createProduct, updateProduct, deleteProduct }
}
```

**useOrders Hook:**
```typescript
export const useOrders = () => {
  const createOrder = async (orderData: {
    customer_name: string
    customer_phone: string
    license_plate?: string
    notes?: string
    items: Array<{
      product_id: string
      vendor_id: string
      product_name: string
      quantity: number
      unit_price: number
    }>
  }) => {
    const total_amount = orderData.items.reduce(
      (sum, item) => sum + (item.quantity * item.unit_price), 0
    )
    
    // Create order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        customer_name: orderData.customer_name,
        customer_phone: orderData.customer_phone,
        license_plate: orderData.license_plate,
        notes: orderData.notes,
        total_amount
      })
      .select()
      .single()
    
    if (orderError) return { data: null, error: orderError }
    
    // Create order items
    const orderItems = orderData.items.map(item => ({
      order_id: order.id,
      ...item,
      subtotal: item.quantity * item.unit_price
    }))
    
    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems)
    
    if (itemsError) return { data: null, error: itemsError }
    
    // Update product stock
    for (const item of orderData.items) {
      await supabase.rpc('decrement_stock', {
        product_id: item.product_id,
        quantity: item.quantity
      })
    }
    
    return { data: order, error: null }
  }
  
  const getVendorOrders = async (vendorId: string) => {
    return await supabase
      .from('orders')
      .select(`
        *,
        order_items!inner(
          *,
          product:products(name)
        )
      `)
      .eq('order_items.vendor_id', vendorId)
  }
  
  const updateOrderStatus = async (orderId: string, status: Order['status']) => {
    return await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId)
  }
  
  return { createOrder, getVendorOrders, updateOrderStatus }
}
```

**Database Functions (add to Supabase):**
```sql
-- Function to decrement product stock
CREATE OR REPLACE FUNCTION decrement_stock(product_id UUID, quantity DECIMAL)
RETURNS VOID AS $
BEGIN
  UPDATE products 
  SET stock = stock - quantity 
  WHERE id = product_id AND stock >= quantity;
END;
$ LANGUAGE plpgsql;
```

Setup proper error handling, loading states, and optimistic updates for all operations.
```

## 第四階段：前端組件開發

### 提示詞 4：客戶端界面組件
```
Create React components for customer-facing interface:

**Layout Components:**
- Header with navigation and cart icon
- Footer with contact info
- Sidebar for categories/vendors filter
- Loading skeletons
- Error boundaries

**Product Components:**
- ProductCard - Display product with image, name, price, unit, vendor
- ProductList - Grid/list view of products
- ProductDetail - Detailed product view with description
- ProductFilter - Filter by category, vendor, price range
- ProductSearch - Search products by name

**Shopping Components:**
- AddToCartButton - Add product to cart with quantity selector
- Cart - Shopping cart sidebar/modal
- CartItem - Individual cart item with quantity controls
- CartSummary - Order total calculation

**Order Components:**
- CheckoutForm - Customer info form (name, phone, license plate, notes)
- OrderConfirmation - Order success page
- OrderSummary - Display order details

**Vendor Display:**
- VendorCard - Display vendor info
- VendorProductList - Products from specific vendor
- VendorFilter - Filter products by vendor

Use Tailwind classes, make components responsive, include proper TypeScript types, and implement loading states.
```

### 提示詞 5：廠商後台界面
```
Create vendor dashboard components:

**Layout:**
- VendorLayout - Dashboard layout with sidebar navigation
- VendorHeader - Header with vendor name and logout
- VendorSidebar - Navigation menu (Dashboard, Products, Orders, Profile)

**Dashboard Components:**
- DashboardStats - Cards showing total products, orders, revenue
- RecentOrders - List of recent orders
- LowStockAlert - Products with low inventory
- QuickActions - Shortcuts to common tasks

**Product Management:**
- ProductForm - Add/edit product form with image upload
- ProductTable - List all vendor products with actions
- ProductStatus - Toggle active/inactive status
- BulkActions - Bulk update products

**Order Management:**
- OrderTable - List orders with filters (status, date)
- OrderDetail - Detailed order view
- OrderStatusUpdate - Update order status buttons
- OrderPrint - Printable order format

**Profile Management:**
- VendorProfile - Edit vendor information
- PasswordChange - Change password form
- AccountSettings - General settings

Include form validation, confirmation dialogs, success/error notifications, and proper loading states.
```

## 第五階段：狀態管理與 API 整合

### 提示詞 6：狀態管理與 API 設定
```
Setup state management and API integration:

**Zustand Stores:**
- authStore - Vendor authentication state
- cartStore - Shopping cart state with persistence
- productStore - Product data and filters
- orderStore - Order management state

**React Query Setup:**
- API client configuration with axios
- Query keys organization
- Custom hooks for:
  - useProducts (with filters)
  - useVendors
  - useOrders
  - useAuth
  - useCart

**API Integration:**
- Create typed API service functions
- Error handling with toast notifications
- Loading states management
- Optimistic updates for cart operations

**Local Storage:**
- Persist cart data
- Remember user preferences
- Save form drafts

**Types:**
Create comprehensive TypeScript interfaces for:
- User, Vendor, Product, Order, OrderItem
- API responses and requests
- Form data types
- Store states

Include proper error boundaries and fallback UI components.
```

## 第六階段：UI/UX 優化

### 提示詞 7：界面設計與體驗
```
Enhance the UI/UX with modern design patterns:

**Design System:**
- Consistent color palette (primary: green theme for fresh produce)
- Typography scale using Inter or similar font
- Spacing and sizing tokens
- Component variants (sizes, states)

**Customer Interface Features:**
- Product image galleries with zoom
- Quick add to cart from product cards
- Floating cart button with item count
- Category navigation with icons
- Vendor badges on products
- Price per unit display
- Stock availability indicators
- Recently viewed products

**Vendor Dashboard Features:**
- Data visualization charts for sales
- Drag-and-drop product image upload
- Bulk product import/export
- Order notification badges
- Quick status update buttons
- Mobile-responsive tables
- Dark mode toggle

**Interactive Elements:**
- Smooth transitions and animations
- Hover effects on cards and buttons
- Loading spinners and skeletons
- Success/error toast notifications
- Confirmation modals
- Progressive form validation

**Accessibility:**
- Proper ARIA labels
- Keyboard navigation
- Screen reader support
- Color contrast compliance
- Focus management

Implement using Tailwind CSS classes and Framer Motion for animations.
```

## 使用建議

### 在 Lovable.ai 中的操作順序：
1. 先使用提示詞 1 建立基礎架構
2. 使用提示詞 2 設計數據模型
3. 依序使用提示詞 3-7 建立各個功能模組
4. 測試各個功能是否正常運作
5. 導出代碼到本地

### 下載到本地後用 Cursor 繼續開發：
- Google Sheets API 整合
- 圖片上傳到雲端存儲
- 高級搜索和篩選功能
- 訂單通知系統
- 數據分析和報表
- 性能優化
- 安全性加強

### 注意事項：
- 每個提示詞可能需要分批執行，視 Lovable.ai 的限制而定
- 建議先完成核心功能再添加高級特性
- 保持代碼結構清晰，方便後續維護