import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  BarChart3,
  Settings,
  HelpCircle,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVendorAuth } from '@/hooks/useVendorAuth';

interface VendorSidebarProps {
  open: boolean;
  onClose: () => void;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  disabled?: boolean;
}

/**
 * 廠商後台側邊欄組件
 */
const VendorSidebar: React.FC<VendorSidebarProps> = ({ open, onClose }) => {
  const location = useLocation();
  const { isActiveVendor, canManageProducts, canManageOrders } = useVendorAuth();

  // 導航項目配置
  const navItems: NavItem[] = [
    {
      title: '儀表板',
      href: '/vendor',
      icon: LayoutDashboard,
    },
    {
      title: '商品管理',
      href: '/vendor/products',
      icon: Package,
      disabled: !canManageProducts,
    },
    {
      title: '訂單管理',
      href: '/vendor/orders',
      icon: ShoppingCart,
      badge: '3', // 這裡可以動態顯示待處理訂單數
      disabled: !canManageOrders,
    },
    {
      title: '數據分析',
      href: '/vendor/analytics',
      icon: BarChart3,
      disabled: !isActiveVendor,
    },
    {
      title: '設定',
      href: '/vendor/settings',
      icon: Settings,
    },
    {
      title: '幫助中心',
      href: '/vendor/help',
      icon: HelpCircle,
    },
  ];

  const isActive = (href: string) => {
    if (href === '/vendor') {
      return location.pathname === '/vendor';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <>
      {/* 側邊欄 */}
      <aside
        className={cn(
          "fixed top-0 left-0 z-40 w-64 h-screen transition-transform duration-300 ease-in-out",
          "bg-white border-r border-gray-200 shadow-lg",
          "lg:translate-x-0", // 桌面版始終顯示
          open ? "translate-x-0" : "-translate-x-full" // 移動版根據狀態顯示/隱藏
        )}
      >
        {/* 側邊欄頭部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-lg font-semibold text-gray-900">
            廠商管理
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="lg:hidden"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* 導航選單 */}
        <nav className="p-4 space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.href);

            return (
              <Link
                key={item.href}
                to={item.href}
                onClick={onClose}
                className={cn(
                  "flex items-center justify-between w-full px-3 py-2.5 text-sm font-medium rounded-lg transition-colors",
                  "hover:bg-gray-100",
                  active && "bg-market-green-50 text-market-green-700 border border-market-green-200",
                  !active && "text-gray-700",
                  item.disabled && "opacity-50 cursor-not-allowed pointer-events-none"
                )}
              >
                <div className="flex items-center space-x-3">
                  <Icon className={cn(
                    "h-5 w-5",
                    active ? "text-market-green-600" : "text-gray-500"
                  )} />
                  <span>{item.title}</span>
                </div>
                
                {item.badge && (
                  <Badge 
                    variant="secondary" 
                    className="bg-red-100 text-red-800 text-xs"
                  >
                    {item.badge}
                  </Badge>
                )}
              </Link>
            );
          })}
        </nav>

        {/* 側邊欄底部 */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-gray-50">
          <div className="text-center">
            <p className="text-xs text-gray-500 mb-2">
              果菜市場廠商後台
            </p>
            <p className="text-xs text-gray-400">
              版本 1.0.0
            </p>
          </div>
        </div>
      </aside>
    </>
  );
};

export default VendorSidebar;
