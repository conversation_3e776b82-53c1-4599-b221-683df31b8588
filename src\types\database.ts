
export interface Vendor {
  id: string;
  name: string;
  phone: string;
  location: string;
  email: string;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED';
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  vendor_id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  unit: 'KG' | 'G' | 'PIECE' | 'BOX' | 'JIN';
  image_url?: string;
  category: string;
  status: 'ACTIVE' | 'INACTIVE';
  created_at: string;
  updated_at: string;
  vendor?: Vendor;
}

export interface Order {
  id: string;
  customer_name: string;
  customer_phone: string;
  license_plate?: string;
  notes?: string;
  total_amount: number;
  status: 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'COMPLETED' | 'CANCELLED';
  created_at: string;
  updated_at: string;
  order_items?: OrderItem[];
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id?: string;
  vendor_id?: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  subtotal: number;
  created_at: string;
  product?: Product;
  vendor?: Vendor;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  sort_order: number;
  created_at: string;
}

// 廠商相關類型定義
export type VendorStatus = 'PENDING' | 'ACTIVE' | 'SUSPENDED';
export type ProductStatus = 'ACTIVE' | 'INACTIVE';
export type OrderStatus = 'PENDING' | 'CONFIRMED' | 'PREPARING' | 'READY' | 'COMPLETED' | 'CANCELLED';

export interface VendorUser {
  id: string;
  email: string;
  vendor?: Vendor;
  role: 'VENDOR' | 'ADMIN' | 'CUSTOMER';
}

export interface VendorDashboardStats {
  totalProducts: number;
  activeProducts: number;
  totalOrders: number;
  pendingOrders: number;
  todayRevenue: number;
  monthlyRevenue: number;
  lowStockProducts: number;
}

export interface VendorOrderFilter {
  status?: OrderStatus;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface VendorProductFilter {
  status?: ProductStatus;
  category?: string;
  search?: string;
  sortBy?: 'name' | 'price' | 'stock' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// 廠商設定相關
export interface VendorSettings {
  id: string;
  vendor_id: string;
  setting_key: string;
  setting_value: any;
  updated_at: string;
}
