var A=function(G){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(U){return typeof U}:function(U){return U&&typeof Symbol=="function"&&U.constructor===Symbol&&U!==Symbol.prototype?"symbol":typeof U},A(G)},R=function(G,U){var Y=Object.keys(G);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(G);U&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(G,N).enumerable})),Y.push.apply(Y,Z)}return Y},x=function(G){for(var U=1;U<arguments.length;U++){var Y=arguments[U]!=null?arguments[U]:{};U%2?R(Object(Y),!0).forEach(function(Z){B0(G,Z,Y[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(Y)):R(Object(Y)).forEach(function(Z){Object.defineProperty(G,Z,Object.getOwnPropertyDescriptor(Y,Z))})}return G},B0=function(G,U,Y){if(U=C0(U),U in G)Object.defineProperty(G,U,{value:Y,enumerable:!0,configurable:!0,writable:!0});else G[U]=Y;return G},C0=function(G){var U=H0(G,"string");return A(U)=="symbol"?U:String(U)},H0=function(G,U){if(A(G)!="object"||!G)return G;var Y=G[Symbol.toPrimitive];if(Y!==void 0){var Z=Y.call(G,U||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(U==="string"?String:Number)(G)};(function(G){var U=Object.defineProperty,Y=function B(H,C){for(var T in C)U(H,T,{get:C[T],enumerable:!0,configurable:!0,set:function X(J){return C[T]=function(){return J}}})},Z={lessThanXSeconds:{one:"inqas minn sekonda",other:"inqas minn {{count}} sekondi"},xSeconds:{one:"sekonda",other:"{{count}} sekondi"},halfAMinute:"nofs minuta",lessThanXMinutes:{one:"inqas minn minuta",other:"inqas minn {{count}} minuti"},xMinutes:{one:"minuta",other:"{{count}} minuti"},aboutXHours:{one:"madwar sieg\u0127a",other:"madwar {{count}} sieg\u0127at"},xHours:{one:"sieg\u0127a",other:"{{count}} sieg\u0127at"},xDays:{one:"\u0121urnata",other:"{{count}} \u0121ranet"},aboutXWeeks:{one:"madwar \u0121img\u0127a",other:"madwar {{count}} \u0121img\u0127at"},xWeeks:{one:"\u0121img\u0127a",other:"{{count}} \u0121img\u0127at"},aboutXMonths:{one:"madwar xahar",other:"madwar {{count}} xhur"},xMonths:{one:"xahar",other:"{{count}} xhur"},aboutXYears:{one:"madwar sena",two:"madwar sentejn",other:"madwar {{count}} snin"},xYears:{one:"sena",two:"sentejn",other:"{{count}} snin"},overXYears:{one:"aktar minn sena",two:"aktar minn sentejn",other:"aktar minn {{count}} snin"},almostXYears:{one:"kwa\u017Ci sena",two:"kwa\u017Ci sentejn",other:"kwa\u017Ci {{count}} snin"}},N=function B(H,C,T){var X,J=Z[H];if(typeof J==="string")X=J;else if(C===1)X=J.one;else if(C===2&&J.two)X=J.two;else X=J.other.replace("{{count}}",String(C));if(T!==null&&T!==void 0&&T.addSuffix)if(T.comparison&&T.comparison>0)return"f'"+X;else return X+" ilu";return X};function z(B){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=H.width?String(H.width):B.defaultWidth,T=B.formats[C]||B.formats[B.defaultWidth];return T}}var W={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},D={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},S={date:z({formats:W,defaultWidth:"full"}),time:z({formats:$,defaultWidth:"full"}),dateTime:z({formats:D,defaultWidth:"full"})},L={lastWeek:"eeee 'li g\u0127adda' 'fil-'p",yesterday:"'Il-biera\u0127 fil-'p",today:"'Illum fil-'p",tomorrow:"'G\u0127ada fil-'p",nextWeek:"eeee 'fil-'p",other:"P"},j=function B(H,C,T,X){return L[H]};function Q(B){return function(H,C){var T=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",X;if(T==="formatting"&&B.formattingValues){var J=B.defaultFormattingWidth||B.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):J;X=B.formattingValues[I]||B.formattingValues[J]}else{var E=B.defaultWidth,K=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;X=B.values[K]||B.values[E]}var O=B.argumentCallback?B.argumentCallback(H):H;return X[O]}}var V={narrow:["Q","W"],abbreviated:["QK","WK"],wide:["qabel Kristu","wara Kristu"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1. kwart","2. kwart","3. kwart","4. kwart"]},w={narrow:["J","F","M","A","M","\u0120","L","A","S","O","N","D"],abbreviated:["Jan","Fra","Mar","Apr","Mej","\u0120un","Lul","Aww","Set","Ott","Nov","Di\u010B"],wide:["Jannar","Frar","Marzu","April","Mejju","\u0120unju","Lulju","Awwissu","Settembru","Ottubru","Novembru","Di\u010Bembru"]},P={narrow:["\u0126","T","T","E","\u0126","\u0120","S"],short:["\u0126a","Tn","Tl","Er","\u0126a","\u0120i","Si"],abbreviated:["\u0126ad","Tne","Tli","Erb","\u0126am","\u0120im","Sib"],wide:["Il-\u0126add","It-Tnejn","It-Tlieta","L-Erbg\u0127a","Il-\u0126amis","Il-\u0120img\u0127a","Is-Sibt"]},v={narrow:{am:"a",pm:"p",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"},abbreviated:{am:"AM",pm:"PM",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"},wide:{am:"a.m.",pm:"p.m.",midnight:"nofsillejl",noon:"nofsinhar",morning:"g\u0127odwa",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"lejl"}},_={narrow:{am:"a",pm:"p",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"},abbreviated:{am:"AM",pm:"PM",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"},wide:{am:"a.m.",pm:"p.m.",midnight:"f'nofsillejl",noon:"f'nofsinhar",morning:"filg\u0127odu",afternoon:"wara nofsinhar",evening:"filg\u0127axija",night:"billejl"}},F=function B(H,C){var T=Number(H);return T+"\xBA"},b={ordinalNumber:F,era:Q({values:V,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function B(H){return H-1}}),month:Q({values:w,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:v,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function q(B){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},T=C.width,X=T&&B.matchPatterns[T]||B.matchPatterns[B.defaultMatchWidth],J=H.match(X);if(!J)return null;var I=J[0],E=T&&B.parsePatterns[T]||B.parsePatterns[B.defaultParseWidth],K=Array.isArray(E)?k(E,function(M){return M.test(I)}):h(E,function(M){return M.test(I)}),O;O=B.valueCallback?B.valueCallback(K):K,O=C.valueCallback?C.valueCallback(O):O;var t=H.slice(I.length);return{value:O,rest:t}}}var h=function B(H,C){for(var T in H)if(Object.prototype.hasOwnProperty.call(H,T)&&C(H[T]))return T;return},k=function B(H,C){for(var T=0;T<H.length;T++)if(C(H[T]))return T;return};function m(B){return function(H){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},T=H.match(B.matchPattern);if(!T)return null;var X=T[0],J=H.match(B.parsePattern);if(!J)return null;var I=B.valueCallback?B.valueCallback(J[0]):J[0];I=C.valueCallback?C.valueCallback(I):I;var E=H.slice(X.length);return{value:I,rest:E}}}var y=/^(\d+)(º)?/i,c=/\d+/i,p={narrow:/^(q|w)/i,abbreviated:/^(q\.?\s?k\.?|b\.?\s?c\.?\s?e\.?|w\.?\s?k\.?)/i,wide:/^(qabel kristu|before common era|wara kristu|common era)/i},g={any:[/^(q|b)/i,/^(w|c)/i]},d={narrow:/^[1234]/i,abbreviated:/^k[1234]/i,wide:/^[1234](\.)? kwart/i},u={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmaglsond]/i,abbreviated:/^(jan|fra|mar|apr|mej|ġun|lul|aww|set|ott|nov|diċ)/i,wide:/^(jannar|frar|marzu|april|mejju|ġunju|lulju|awwissu|settembru|ottubru|novembru|diċembru)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^ġ/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mej/i,/^ġ/i,/^l/i,/^aw/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[ħteġs]/i,short:/^(ħa|tn|tl|er|ħa|ġi|si)/i,abbreviated:/^(ħad|tne|tli|erb|ħam|ġim|sib)/i,wide:/^(il-ħadd|it-tnejn|it-tlieta|l-erbgħa|il-ħamis|il-ġimgħa|is-sibt)/i},s={narrow:[/^ħ/i,/^t/i,/^t/i,/^e/i,/^ħ/i,/^ġ/i,/^s/i],any:[/^(il-)?ħad/i,/^(it-)?tn/i,/^(it-)?tl/i,/^(l-)?er/i,/^(il-)?ham/i,/^(il-)?ġi/i,/^(is-)?si/i]},o={narrow:/^(a|p|f'nofsillejl|f'nofsinhar|(ta') (għodwa|wara nofsinhar|filgħaxija|lejl))/i,any:/^([ap]\.?\s?m\.?|f'nofsillejl|f'nofsinhar|(ta') (għodwa|wara nofsinhar|filgħaxija|lejl))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/^f'nofsillejl/i,noon:/^f'nofsinhar/i,morning:/għodwa/i,afternoon:/wara(\s.*)nofsinhar/i,evening:/filgħaxija/i,night:/lejl/i}},a={ordinalNumber:m({matchPattern:y,parsePattern:c,valueCallback:function B(H){return parseInt(H,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any",valueCallback:function B(H){return H+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"mt",formatDistance:N,formatLong:S,formatRelative:j,localize:b,match:a,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{mt:e})})})();

//# debugId=933B4ED109BCB51264756e2164756e21
