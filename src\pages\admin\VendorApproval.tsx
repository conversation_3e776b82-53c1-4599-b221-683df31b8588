import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Eye, Phone, MapPin, Mail, Calendar } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface Vendor {
  id: string;
  name: string;
  phone: string;
  location: string;
  email: string;
  status: string;
  created_at: string;
  updated_at: string;
}

const VendorApproval = () => {
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const queryClient = useQueryClient();

  // 獲取待審核廠商列表
  const { data: vendors, isLoading } = useQuery({
    queryKey: ['pending-vendors'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Vendor[];
    }
  });

  // 審核廠商 Mutation
  const approveVendorMutation = useMutation({
    mutationFn: async (vendorId: string) => {
      const { error } = await supabase
        .from('vendors')
        .update({ 
          status: 'ACTIVE',
          updated_at: new Date().toISOString()
        })
        .eq('id', vendorId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pending-vendors'] });
      toast.success('廠商審核通過！');
      setShowApprovalDialog(false);
      setSelectedVendor(null);
    },
    onError: (error) => {
      toast.error('審核失敗：' + error.message);
    }
  });

  // 拒絕廠商 Mutation
  const rejectVendorMutation = useMutation({
    mutationFn: async ({ vendorId, reason }: { vendorId: string; reason: string }) => {
      const { error } = await supabase
        .from('vendors')
        .update({ 
          status: 'REJECTED',
          updated_at: new Date().toISOString()
        })
        .eq('id', vendorId);

      if (error) throw error;
      
      // 這裡可以添加發送拒絕通知的邏輯
      console.log('拒絕原因:', reason);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pending-vendors'] });
      toast.success('已拒絕廠商申請');
      setShowRejectDialog(false);
      setSelectedVendor(null);
      setRejectReason('');
    },
    onError: (error) => {
      toast.error('操作失敗：' + error.message);
    }
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="text-orange-600">待審核</Badge>;
      case 'ACTIVE':
        return <Badge variant="default" className="text-green-600">已啟用</Badge>;
      case 'REJECTED':
        return <Badge variant="destructive">已拒絕</Badge>;
      case 'SUSPENDED':
        return <Badge variant="secondary">已暫停</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-TW');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">載入中...</div>
      </div>
    );
  }

  const pendingVendors = vendors?.filter(v => v.status === 'PENDING') || [];
  const allVendors = vendors || [];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">廠商審核管理</h1>
        <p className="text-gray-600 mt-2">審核新註冊的廠商申請</p>
      </div>

      {/* 統計摘要 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{pendingVendors.length}</div>
            <div className="text-sm text-gray-600">待審核</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {allVendors.filter(v => v.status === 'ACTIVE').length}
            </div>
            <div className="text-sm text-gray-600">已啟用</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {allVendors.filter(v => v.status === 'REJECTED').length}
            </div>
            <div className="text-sm text-gray-600">已拒絕</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{allVendors.length}</div>
            <div className="text-sm text-gray-600">總計</div>
          </CardContent>
        </Card>
      </div>

      {/* 廠商列表 */}
      <Card>
        <CardHeader>
          <CardTitle>廠商申請列表</CardTitle>
          <CardDescription>
            管理所有廠商申請，優先處理待審核項目
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>廠商名稱</TableHead>
                <TableHead>聯絡資訊</TableHead>
                <TableHead>攤位位置</TableHead>
                <TableHead>申請時間</TableHead>
                <TableHead>狀態</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {allVendors.map((vendor) => (
                <TableRow key={vendor.id} className={vendor.status === 'PENDING' ? 'bg-orange-50' : ''}>
                  <TableCell className="font-medium">{vendor.name}</TableCell>
                  <TableCell>
                    <div className="space-y-1 text-sm">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {vendor.email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {vendor.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {vendor.location}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <Calendar className="h-3 w-3" />
                      {formatDate(vendor.created_at)}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(vendor.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedVendor(vendor)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        查看
                      </Button>
                      {vendor.status === 'PENDING' && (
                        <>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => {
                              setSelectedVendor(vendor);
                              setShowApprovalDialog(true);
                            }}
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            通過
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setSelectedVendor(vendor);
                              setShowRejectDialog(true);
                            }}
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            拒絕
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 審核通過確認對話框 */}
      <Dialog open={showApprovalDialog} onOpenChange={setShowApprovalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>確認審核通過</DialogTitle>
            <DialogDescription>
              您確定要通過 "{selectedVendor?.name}" 的廠商申請嗎？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApprovalDialog(false)}>
              取消
            </Button>
            <Button 
              onClick={() => selectedVendor && approveVendorMutation.mutate(selectedVendor.id)}
              disabled={approveVendorMutation.isPending}
            >
              確認通過
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 拒絕申請對話框 */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>拒絕廠商申請</DialogTitle>
            <DialogDescription>
              請說明拒絕 "{selectedVendor?.name}" 申請的原因
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason">拒絕原因</Label>
              <Textarea
                id="reason"
                placeholder="請輸入拒絕原因..."
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              取消
            </Button>
            <Button 
              variant="destructive"
              onClick={() => selectedVendor && rejectVendorMutation.mutate({
                vendorId: selectedVendor.id,
                reason: rejectReason
              })}
              disabled={rejectVendorMutation.isPending || !rejectReason.trim()}
            >
              確認拒絕
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VendorApproval;
