
import React from 'react';
import { useCategories } from '@/hooks/useCategories';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

interface CategoryFilterProps {
  selectedCategory?: string;
  onCategorySelect: (category?: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  selectedCategory,
  onCategorySelect
}) => {
  const { data: categories, isLoading } = useCategories();

  if (isLoading) {
    return (
      <div className="flex gap-2 overflow-x-auto pb-2">
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton key={index} className="h-10 w-20 flex-shrink-0" />
        ))}
      </div>
    );
  }

  return (
    <div className="flex gap-2 overflow-x-auto pb-2">
      <Button
        variant={!selectedCategory ? "default" : "outline"}
        onClick={() => onCategorySelect(undefined)}
        className="flex-shrink-0"
      >
        全部
      </Button>
      {categories?.map((category) => (
        <Button
          key={category.id}
          variant={selectedCategory === category.name ? "default" : "outline"}
          onClick={() => onCategorySelect(category.name)}
          className="flex-shrink-0"
        >
          {category.name}
        </Button>
      ))}
    </div>
  );
};

export default CategoryFilter;
