import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useVendorAuth } from './useVendorAuth';
import { toast } from 'sonner';
import type { Product, VendorProductFilter } from '@/types/database';

/**
 * 廠商商品管理 Hook
 * 提供商品的 CRUD 操作功能
 */
export const useVendorProducts = (filters?: VendorProductFilter) => {
  const { vendor, isActiveVendor } = useVendorAuth();
  const queryClient = useQueryClient();

  // 獲取廠商商品列表
  const {
    data: products,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['vendor-products', vendor?.id, filters],
    queryFn: async (): Promise<{ data: Product[]; count: number }> => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      let query = supabase
        .from('products')
        .select('*', { count: 'exact' })
        .eq('vendor_id', vendor.id);

      // 應用篩選條件
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      // 排序
      const sortBy = filters?.sortBy || 'created_at';
      const sortOrder = filters?.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // 分頁
      const page = filters?.page || 1;
      const limit = filters?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data as Product[],
        count: count || 0
      };
    },
    enabled: !!vendor?.id && isActiveVendor,
    staleTime: 2 * 60 * 1000, // 2 分鐘
  });

  // 新增商品
  const createProduct = useMutation({
    mutationFn: async (productData: Omit<Product, 'id' | 'vendor_id' | 'created_at' | 'updated_at'>) => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      const { data, error } = await supabase
        .from('products')
        .insert({
          ...productData,
          vendor_id: vendor.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-products'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success('商品新增成功！');
    },
    onError: (error: any) => {
      console.error('Create product error:', error);
      toast.error(error.message || '商品新增失敗');
    }
  });

  // 更新商品
  const updateProduct = useMutation({
    mutationFn: async ({ id, ...productData }: Partial<Product> & { id: string }) => {
      const { data, error } = await supabase
        .from('products')
        .update({
          ...productData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('vendor_id', vendor?.id) // 確保只能更新自己的商品
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-products'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success('商品更新成功！');
    },
    onError: (error: any) => {
      console.error('Update product error:', error);
      toast.error(error.message || '商品更新失敗');
    }
  });

  // 刪除商品
  const deleteProduct = useMutation({
    mutationFn: async (productId: string) => {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId)
        .eq('vendor_id', vendor?.id); // 確保只能刪除自己的商品

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vendor-products'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success('商品刪除成功！');
    },
    onError: (error: any) => {
      console.error('Delete product error:', error);
      toast.error(error.message || '商品刪除失敗');
    }
  });

  // 批量更新商品狀態
  const bulkUpdateStatus = useMutation({
    mutationFn: async ({ productIds, status }: { productIds: string[]; status: 'ACTIVE' | 'INACTIVE' }) => {
      const { error } = await supabase
        .from('products')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .in('id', productIds)
        .eq('vendor_id', vendor?.id);

      if (error) throw error;
    },
    onSuccess: (_, { productIds, status }) => {
      queryClient.invalidateQueries({ queryKey: ['vendor-products'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success(`已${status === 'ACTIVE' ? '上架' : '下架'} ${productIds.length} 個商品`);
    },
    onError: (error: any) => {
      console.error('Bulk update error:', error);
      toast.error(error.message || '批量更新失敗');
    }
  });

  // 批量刪除商品
  const bulkDeleteProducts = useMutation({
    mutationFn: async (productIds: string[]) => {
      const { error } = await supabase
        .from('products')
        .delete()
        .in('id', productIds)
        .eq('vendor_id', vendor?.id);

      if (error) throw error;
    },
    onSuccess: (_, productIds) => {
      queryClient.invalidateQueries({ queryKey: ['vendor-products'] });
      queryClient.invalidateQueries({ queryKey: ['vendor-dashboard'] });
      toast.success(`已刪除 ${productIds.length} 個商品`);
    },
    onError: (error: any) => {
      console.error('Bulk delete error:', error);
      toast.error(error.message || '批量刪除失敗');
    }
  });

  return {
    // 數據
    products: products?.data || [],
    totalCount: products?.count || 0,
    isLoading,
    error,
    refetch,

    // 操作
    createProduct: createProduct.mutate,
    updateProduct: updateProduct.mutate,
    deleteProduct: deleteProduct.mutate,
    bulkUpdateStatus: bulkUpdateStatus.mutate,
    bulkDeleteProducts: bulkDeleteProducts.mutate,

    // 狀態
    isCreating: createProduct.isPending,
    isUpdating: updateProduct.isPending,
    isDeleting: deleteProduct.isPending,
    isBulkUpdating: bulkUpdateStatus.isPending,
    isBulkDeleting: bulkDeleteProducts.isPending,
  };
};
