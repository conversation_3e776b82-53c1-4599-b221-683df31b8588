import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Store, ShoppingCart, Clock } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

const AdminDashboard = () => {
  // 獲取統計數據
  const { data: stats, isLoading } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: async () => {
      const [vendorsResult, productsResult, ordersResult] = await Promise.all([
        supabase.from('vendors').select('status'),
        supabase.from('products').select('status'),
        supabase.from('orders').select('status')
      ]);

      const vendors = vendorsResult.data || [];
      const products = productsResult.data || [];
      const orders = ordersResult.data || [];

      return {
        totalVendors: vendors.length,
        pendingVendors: vendors.filter(v => v.status === 'PENDING').length,
        activeVendors: vendors.filter(v => v.status === 'ACTIVE').length,
        totalProducts: products.length,
        activeProducts: products.filter(p => p.status === 'ACTIVE').length,
        totalOrders: orders.length,
        pendingOrders: orders.filter(o => o.status === 'PENDING').length
      };
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">載入中...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">管理員控制台</h1>
        <p className="text-gray-600 mt-2">管理廠商、商品和訂單</p>
      </div>

      {/* 統計卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">廠商總數</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalVendors || 0}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
              <Badge variant="secondary">{stats?.activeVendors || 0} 已啟用</Badge>
              <Badge variant="outline">{stats?.pendingVendors || 0} 待審核</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">商品總數</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalProducts || 0}</div>
            <div className="text-xs text-muted-foreground">
              {stats?.activeProducts || 0} 個商品上架中
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">訂單總數</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalOrders || 0}</div>
            <div className="text-xs text-muted-foreground">
              {stats?.pendingOrders || 0} 個待處理訂單
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待審核項目</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(stats?.pendingVendors || 0)}
            </div>
            <div className="text-xs text-muted-foreground">
              需要立即處理
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>廠商審核</CardTitle>
            <CardDescription>
              審核新註冊的廠商申請
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span>待審核廠商</span>
                <Badge variant="outline">{stats?.pendingVendors || 0}</Badge>
              </div>
              <a 
                href="/admin/vendors" 
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
              >
                前往審核
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>系統監控</CardTitle>
            <CardDescription>
              監控系統運行狀態
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span>系統狀態</span>
                <Badge variant="default">正常運行</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span>資料庫連線</span>
                <Badge variant="default">已連接</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
