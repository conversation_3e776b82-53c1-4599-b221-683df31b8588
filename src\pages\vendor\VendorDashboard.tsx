import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  ShoppingCart,
  TrendingUp,
  AlertTriangle,
  Plus,
  Eye,
  DollarSign,
  Users
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useVendorDashboard, useVendorRecentOrders, useVendorLowStockProducts } from '@/hooks/useVendorDashboard';
import { useVendorAuth } from '@/hooks/useVendorAuth';
import { cn } from '@/lib/utils';

/**
 * 廠商儀表板主頁面
 */
const VendorDashboard: React.FC = () => {
  const { vendor } = useVendorAuth();
  const { stats, isLoading: statsLoading } = useVendorDashboard();
  const { data: recentOrders, isLoading: ordersLoading } = useVendorRecentOrders(5);
  const { data: lowStockProducts, isLoading: stockLoading } = useVendorLowStockProducts(10);

  // 統計卡片數據
  const statCards = [
    {
      title: '總商品數',
      value: stats?.totalProducts || 0,
      description: `${stats?.activeProducts || 0} 個商品上架中`,
      icon: Package,
      color: 'blue',
      href: '/vendor/products'
    },
    {
      title: '總訂單數',
      value: stats?.totalOrders || 0,
      description: `${stats?.pendingOrders || 0} 個待處理訂單`,
      icon: ShoppingCart,
      color: 'green',
      href: '/vendor/orders'
    },
    {
      title: '今日收益',
      value: `$${(stats?.todayRevenue || 0).toLocaleString()}`,
      description: `本月 $${(stats?.monthlyRevenue || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'yellow',
      href: '/vendor/analytics'
    },
    {
      title: '低庫存警告',
      value: stats?.lowStockProducts || 0,
      description: '需要補貨的商品',
      icon: AlertTriangle,
      color: 'red',
      href: '/vendor/products?filter=low-stock'
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-600',
      green: 'bg-green-50 text-green-600',
      yellow: 'bg-yellow-50 text-yellow-600',
      red: 'bg-red-50 text-red-600'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            歡迎回來，{vendor?.name}
          </h1>
          <p className="text-gray-600 mt-1">
            這是您的廠商管理儀表板
          </p>
        </div>
        <div className="flex space-x-3">
          <Button asChild>
            <Link to="/vendor/products/new">
              <Plus className="h-4 w-4 mr-2" />
              新增商品
            </Link>
          </Button>
        </div>
      </div>

      {/* 統計卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card) => {
          const Icon = card.icon;
          return (
            <Card key={card.title} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {card.title}
                </CardTitle>
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center",
                  getColorClasses(card.color)
                )}>
                  <Icon className="h-4 w-4" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {statsLoading ? '...' : card.value}
                </div>
                <p className="text-xs text-gray-500">
                  {card.description}
                </p>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  asChild 
                  className="mt-2 p-0 h-auto text-xs"
                >
                  <Link to={card.href}>
                    查看詳情 <Eye className="h-3 w-3 ml-1" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近訂單 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              最近訂單
              <Button variant="outline" size="sm" asChild>
                <Link to="/vendor/orders">
                  查看全部
                </Link>
              </Button>
            </CardTitle>
            <CardDescription>
              最新的 5 筆訂單
            </CardDescription>
          </CardHeader>
          <CardContent>
            {ordersLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : recentOrders && recentOrders.length > 0 ? (
              <div className="space-y-4">
                {recentOrders.map((item: any) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-sm">{item.product_name}</p>
                      <p className="text-xs text-gray-500">
                        客戶: {item.orders.customer_name} | 
                        數量: {item.quantity} | 
                        金額: ${item.subtotal}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {item.orders.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">
                暫無訂單
              </p>
            )}
          </CardContent>
        </Card>

        {/* 低庫存商品 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              庫存警告
              <Button variant="outline" size="sm" asChild>
                <Link to="/vendor/products?filter=low-stock">
                  查看全部
                </Link>
              </Button>
            </CardTitle>
            <CardDescription>
              庫存不足的商品
            </CardDescription>
          </CardHeader>
          <CardContent>
            {stockLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : lowStockProducts && lowStockProducts.length > 0 ? (
              <div className="space-y-4">
                {lowStockProducts.map((product: any) => (
                  <div key={product.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex-1">
                      <p className="font-medium text-sm">{product.name}</p>
                      <p className="text-xs text-gray-500">
                        剩餘庫存: {product.stock} {product.unit}
                      </p>
                    </div>
                    <Badge variant="destructive" className="text-xs">
                      低庫存
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">
                所有商品庫存充足
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default VendorDashboard;
