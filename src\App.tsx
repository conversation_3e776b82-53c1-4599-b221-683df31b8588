
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Checkout from "./pages/Checkout";
import Orders from "./pages/Orders";
import OrderDetail from "./pages/OrderDetail";
import NotFound from "./pages/NotFound";
import Login from "./pages/auth/Login";
import Register from "./pages/auth/Register";
import VendorRegister from "./pages/auth/VendorRegister";

// 廠商後台相關組件
import VendorProtectedRoute from "./components/auth/VendorProtectedRoute";
import VendorLayout from "./components/vendor/VendorLayout";
import VendorDashboard from "./pages/vendor/VendorDashboard";
// import VendorProducts from "./pages/vendor/VendorProducts";
// import VendorProductNew from "./pages/vendor/VendorProductNew";
// import VendorOrders from "./pages/vendor/VendorOrders";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/products" element={<Products />} />
          <Route path="/products/:id" element={<ProductDetail />} />

          {/* 購物車和訂單相關路由 */}
          <Route path="/checkout" element={<Checkout />} />
          <Route path="/orders" element={<Orders />} />
          <Route path="/orders/:id" element={<OrderDetail />} />

          {/* 認證相關路由 */}
          <Route path="/auth/login" element={<Login />} />
          <Route path="/auth/register" element={<Register />} />
          <Route path="/auth/vendor-register" element={<VendorRegister />} />

          {/* 廠商後台路由 */}
          <Route path="/vendor" element={
            <VendorProtectedRoute requireActive={false}>
              <VendorLayout />
            </VendorProtectedRoute>
          }>
            <Route index element={<VendorDashboard />} />

            {/* 商品管理路由 - 暫時註釋 */}
            {/* <Route path="products" element={
              <VendorProtectedRoute requireActive={true}>
                <VendorProducts />
              </VendorProtectedRoute>
            } />
            <Route path="products/new" element={
              <VendorProtectedRoute requireActive={true}>
                <VendorProductNew />
              </VendorProtectedRoute>
            } /> */}

            {/* 訂單管理路由 - 暫時註釋 */}
            {/* <Route path="orders" element={
              <VendorProtectedRoute requireActive={true}>
                <VendorOrders />
              </VendorProtectedRoute>
            } /> */}

            {/* 未來會在這裡添加更多廠商後台路由 */}
            {/* <Route path="analytics" element={<VendorAnalytics />} /> */}
            {/* <Route path="settings" element={<VendorSettings />} /> */}
          </Route>

          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
