
import React from 'react';
import { useProducts } from '@/hooks/useProducts';
import ProductCard from './ProductCard';
import { Skeleton } from '@/components/ui/skeleton';

const ProductGrid = () => {
  const { data: products, isLoading, error } = useProducts();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="space-y-4">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 text-lg">載入產品時發生錯誤</p>
        <p className="text-gray-500 mt-2">請稍後再試</p>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 text-lg">目前沒有可用的產品</p>
        <p className="text-gray-500 mt-2">請稍後再查看</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          id={product.id}
          name={product.name}
          price={product.price}
          unit={product.unit}
          image={product.image_url || '/placeholder.svg'}
          vendor={product.vendor?.name || '未知供應商'}
          location={product.vendor?.location || '未知地點'}
          rating={4.5} // 暫時固定評分，之後可以從評價系統獲取
          reviewCount={Math.floor(Math.random() * 200) + 10} // 暫時隨機評價數
          inStock={product.stock > 0}
          stock={product.stock}
          minOrder={product.unit === 'KG' ? 5 : product.unit === 'G' ? 500 : 1}
        />
      ))}
    </div>
  );
};

export default ProductGrid;
