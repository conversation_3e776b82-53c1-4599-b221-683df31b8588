import { z } from "zod";

// 登入表單驗證
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, "請輸入電子郵件")
    .email("請輸入有效的電子郵件格式"),
  password: z
    .string()
    .min(1, "請輸入密碼")
    .min(6, "密碼至少需要 6 個字元"),
});

// 註冊表單驗證
export const registerSchema = z.object({
  email: z
    .string()
    .min(1, "請輸入電子郵件")
    .email("請輸入有效的電子郵件格式"),
  password: z
    .string()
    .min(1, "請輸入密碼")
    .min(6, "密碼至少需要 6 個字元")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "密碼必須包含至少一個大寫字母、一個小寫字母和一個數字"
    ),
  confirmPassword: z
    .string()
    .min(1, "請確認密碼"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "密碼確認不一致",
  path: ["confirmPassword"],
});

// 廠商註冊表單驗證
export const vendorRegisterSchema = z.object({
  // 帳戶資訊
  email: z
    .string()
    .min(1, "請輸入電子郵件")
    .email("請輸入有效的電子郵件格式"),
  password: z
    .string()
    .min(1, "請輸入密碼")
    .min(6, "密碼至少需要 6 個字元")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "密碼必須包含至少一個大寫字母、一個小寫字母和一個數字"
    ),
  confirmPassword: z
    .string()
    .min(1, "請確認密碼"),
  
  // 廠商資訊
  name: z
    .string()
    .min(1, "請輸入廠商名稱")
    .min(2, "廠商名稱至少需要 2 個字元")
    .max(50, "廠商名稱不能超過 50 個字元"),
  phone: z
    .string()
    .min(1, "請輸入聯絡電話")
    .regex(
      /^(\+886|0)?[2-9]\d{7,8}$/,
      "請輸入有效的台灣電話號碼格式"
    ),
  location: z
    .string()
    .min(1, "請輸入攤位位置")
    .min(2, "攤位位置至少需要 2 個字元")
    .max(100, "攤位位置不能超過 100 個字元"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "密碼確認不一致",
  path: ["confirmPassword"],
});

// 結帳表單驗證
export const checkoutSchema = z.object({
  // 客戶資訊
  customerName: z
    .string()
    .min(1, "請輸入客戶姓名")
    .min(2, "姓名至少需要 2 個字元")
    .max(50, "姓名不能超過 50 個字元"),
  customerPhone: z
    .string()
    .min(1, "請輸入聯絡電話")
    .regex(
      /^(\+886|0)?[2-9]\d{7,8}$/,
      "請輸入有效的台灣電話號碼格式"
    ),
  licensePlate: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 2, {
      message: "車牌號碼至少需要 2 個字元"
    }),
  notes: z
    .string()
    .max(500, "備註不能超過 500 個字元")
    .optional(),
});

// TypeScript 類型定義
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type VendorRegisterFormData = z.infer<typeof vendorRegisterSchema>;
export type CheckoutFormData = z.infer<typeof checkoutSchema>;
