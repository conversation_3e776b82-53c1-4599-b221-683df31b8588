
import { useState, useEffect } from 'react';

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  // Get from local storage then parse stored json or return initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: T | ((prev: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`<PERSON>rror setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

// Hook for user preferences
export function useUserPreferences() {
  const [preferences, setPreferences] = useLocalStorage('user-preferences', {
    theme: 'light',
    language: 'zh-TW',
    currency: 'TWD',
    viewMode: 'grid',
    itemsPerPage: 12,
  });

  return { preferences, setPreferences };
}

// Hook for form drafts
export function useFormDraft<T extends Record<string, any>>(
  formKey: string,
  initialData: T
) {
  const [draft, setDraft] = useLocalStorage(`form-draft-${formKey}`, initialData);

  const updateDraft = (updates: Partial<T>) => {
    setDraft(prev => ({ ...prev, ...updates }));
  };

  const clearDraft = () => {
    setDraft(initialData);
  };

  return { draft, updateDraft, clearDraft };
}
