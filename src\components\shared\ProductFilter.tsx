import React from 'react';
import { Filter, ChevronDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export interface FilterOptions {
  categories: string[];
  priceRange: {
    min: number;
    max: number;
  };
  sortBy: 'name' | 'price_asc' | 'price_desc' | 'newest' | 'rating';
  inStockOnly: boolean;
  isOrganic: boolean;
}

interface ProductFilterProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  availableCategories: string[];
  className?: string;
}

const ProductFilter: React.FC<ProductFilterProps> = ({
  filters,
  onFiltersChange,
  availableCategories,
  className = ""
}) => {
  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category];
    
    onFiltersChange({
      ...filters,
      categories: newCategories
    });
  };

  const handleSortChange = (sortBy: FilterOptions['sortBy']) => {
    onFiltersChange({
      ...filters,
      sortBy
    });
  };

  const handleToggleFilter = (key: 'inStockOnly' | 'isOrganic') => {
    onFiltersChange({
      ...filters,
      [key]: !filters[key]
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      priceRange: { min: 0, max: 1000 },
      sortBy: 'name',
      inStockOnly: false,
      isOrganic: false
    });
  };

  const activeFiltersCount = 
    filters.categories.length + 
    (filters.inStockOnly ? 1 : 0) + 
    (filters.isOrganic ? 1 : 0);

  const sortOptions = [
    { value: 'name', label: '名稱排序' },
    { value: 'price_asc', label: '價格：低到高' },
    { value: 'price_desc', label: '價格：高到低' },
    { value: 'newest', label: '最新上架' },
    { value: 'rating', label: '評分最高' },
  ];

  return (
    <div className={`flex flex-wrap items-center gap-3 ${className}`}>
      {/* 排序選擇 */}
      <Select value={filters.sortBy} onValueChange={handleSortChange}>
        <SelectTrigger className="w-40">
          <SelectValue placeholder="排序方式" />
        </SelectTrigger>
        <SelectContent>
          {sortOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* 分類篩選 */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="relative">
            <Filter className="w-4 h-4 mr-2" />
            分類篩選
            {filters.categories.length > 0 && (
              <Badge className="ml-2 bg-green-600 text-white">
                {filters.categories.length}
              </Badge>
            )}
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>商品分類</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {availableCategories.map((category) => (
            <DropdownMenuCheckboxItem
              key={category}
              checked={filters.categories.includes(category)}
              onCheckedChange={() => handleCategoryToggle(category)}
            >
              {category}
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 其他篩選選項 */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="relative">
            更多篩選
            {(filters.inStockOnly || filters.isOrganic) && (
              <Badge className="ml-2 bg-green-600 text-white">
                {(filters.inStockOnly ? 1 : 0) + (filters.isOrganic ? 1 : 0)}
              </Badge>
            )}
            <ChevronDown className="w-4 h-4 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          <DropdownMenuLabel>篩選條件</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.inStockOnly}
            onCheckedChange={() => handleToggleFilter('inStockOnly')}
          >
            僅顯示有庫存
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.isOrganic}
            onCheckedChange={() => handleToggleFilter('isOrganic')}
          >
            有機商品
          </DropdownMenuCheckboxItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* 清除所有篩選 */}
      {activeFiltersCount > 0 && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={clearAllFilters}
          className="text-gray-600 hover:text-gray-900"
        >
          清除篩選 ({activeFiltersCount})
        </Button>
      )}

      {/* 已選擇的分類標籤 */}
      {filters.categories.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {filters.categories.map((category) => (
            <Badge
              key={category}
              variant="secondary"
              className="cursor-pointer hover:bg-gray-200"
              onClick={() => handleCategoryToggle(category)}
            >
              {category}
              <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductFilter;
