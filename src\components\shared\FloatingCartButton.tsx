
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, X } from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import CartSidebar from './CartSidebar';

const FloatingCartButton: React.FC = () => {
  const { getTotalItems, getTotalAmount, isOpen, toggleCart } = useCart();
  const [isVisible, setIsVisible] = useState(true);
  
  const totalItems = getTotalItems();
  const totalAmount = getTotalAmount();

  if (totalItems === 0) return null;

  return (
    <>
      {isVisible && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end space-y-2">
          {/* Cart Summary */}
          <div className="bg-white rounded-lg shadow-lg border p-3 animate-in slide-in-from-bottom-4">
            <div className="text-sm">
              <div className="font-medium text-gray-900">
                {totalItems} 件商品
              </div>
              <div className="text-market-green-600 font-bold">
                總計 ${totalAmount}
              </div>
            </div>
          </div>

          {/* Floating Button */}
          <div className="relative">
            <Button
              size="lg"
              className="w-14 h-14 rounded-full bg-market-green-500 hover:bg-market-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 animate-pulse"
              onClick={toggleCart}
              aria-label={`購物車有 ${totalItems} 件商品，總計 ${totalAmount} 元`}
            >
              <ShoppingCart className="w-6 h-6" />
            </Button>
            
            {/* Item Count Badge */}
            <Badge className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
              {totalItems > 99 ? '99+' : totalItems}
            </Badge>

            {/* Close Button */}
            <button
              onClick={() => setIsVisible(false)}
              className="absolute -top-1 -left-1 w-5 h-5 bg-gray-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-600 transition-colors"
              aria-label="隱藏購物車按鈕"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        </div>
      )}

      <CartSidebar />
    </>
  );
};

export default FloatingCartButton;
