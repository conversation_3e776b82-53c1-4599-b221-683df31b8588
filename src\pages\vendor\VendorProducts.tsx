import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Package,
  AlertTriangle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useVendorProducts } from '@/hooks/useVendorProducts';
import { useVendorProductStore } from '@/stores/vendorStore';
import { cn } from '@/lib/utils';
import type { Product } from '@/types/database';

/**
 * 廠商商品管理頁面
 */
const VendorProducts: React.FC = () => {
  const {
    filters,
    setFilter,
    clearFilters,
    selectedProducts,
    toggleSelection,
    setSelectedProducts,
    bulkActionLoading,
    setBulkActionLoading
  } = useVendorProductStore();

  const {
    products,
    totalCount,
    isLoading,
    deleteProduct,
    bulkUpdateStatus,
    bulkDeleteProducts,
    isDeleting,
    isBulkUpdating,
    isBulkDeleting
  } = useVendorProducts(filters);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<string | null>(null);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);

  // 處理搜尋
  const handleSearch = (value: string) => {
    setFilter('search', value || undefined);
  };

  // 處理篩選
  const handleStatusFilter = (value: string) => {
    setFilter('status', value === 'all' ? undefined : value as any);
  };

  const handleCategoryFilter = (value: string) => {
    setFilter('category', value === 'all' ? undefined : value);
  };

  // 處理排序
  const handleSort = (sortBy: string) => {
    const currentOrder = filters.sortOrder || 'desc';
    const newOrder = filters.sortBy === sortBy && currentOrder === 'desc' ? 'asc' : 'desc';
    setFilter('sortBy', sortBy as any);
    setFilter('sortOrder', newOrder);
  };

  // 處理分頁
  const handlePageChange = (page: number) => {
    setFilter('page', page);
  };

  // 處理單個商品刪除
  const handleDeleteProduct = (productId: string) => {
    setProductToDelete(productId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteProduct = () => {
    if (productToDelete) {
      deleteProduct(productToDelete);
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    }
  };

  // 處理批量操作
  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedProducts.length === 0) return;

    setBulkActionLoading(true);
    try {
      switch (action) {
        case 'activate':
          bulkUpdateStatus({ productIds: selectedProducts, status: 'ACTIVE' });
          break;
        case 'deactivate':
          bulkUpdateStatus({ productIds: selectedProducts, status: 'INACTIVE' });
          break;
        case 'delete':
          setBulkDeleteDialogOpen(true);
          return;
      }
      setSelectedProducts([]);
    } finally {
      setBulkActionLoading(false);
    }
  };

  const confirmBulkDelete = () => {
    bulkDeleteProducts(selectedProducts);
    setBulkDeleteDialogOpen(false);
    setSelectedProducts([]);
  };

  // 全選/取消全選
  const handleSelectAll = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };

  const getStatusBadge = (status: string) => {
    return (
      <Badge 
        variant={status === 'ACTIVE' ? 'default' : 'secondary'}
        className={cn(
          status === 'ACTIVE' && 'bg-green-100 text-green-800',
          status === 'INACTIVE' && 'bg-gray-100 text-gray-800'
        )}
      >
        {status === 'ACTIVE' ? '上架中' : '已下架'}
      </Badge>
    );
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">缺貨</Badge>;
    } else if (stock < 10) {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">低庫存</Badge>;
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">商品管理</h1>
          <p className="text-gray-600 mt-1">
            管理您的商品庫存和狀態
          </p>
        </div>
        <Button asChild>
          <Link to="/vendor/products/new">
            <Plus className="h-4 w-4 mr-2" />
            新增商品
          </Link>
        </Button>
      </div>

      {/* 篩選和搜尋 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">篩選和搜尋</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* 搜尋框 */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜尋商品名稱或描述..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 狀態篩選 */}
            <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部狀態</SelectItem>
                <SelectItem value="ACTIVE">上架中</SelectItem>
                <SelectItem value="INACTIVE">已下架</SelectItem>
              </SelectContent>
            </Select>

            {/* 分類篩選 */}
            <Select value={filters.category || 'all'} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="分類" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分類</SelectItem>
                <SelectItem value="葉菜類">葉菜類</SelectItem>
                <SelectItem value="根莖類">根莖類</SelectItem>
                <SelectItem value="果菜類">果菜類</SelectItem>
                <SelectItem value="菇類">菇類</SelectItem>
                <SelectItem value="辛香料">辛香料</SelectItem>
              </SelectContent>
            </Select>

            {/* 清除篩選 */}
            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              清除篩選
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作 */}
      {selectedProducts.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                已選擇 {selectedProducts.length} 個商品
              </span>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('activate')}
                  disabled={bulkActionLoading || isBulkUpdating}
                >
                  批量上架
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkAction('deactivate')}
                  disabled={bulkActionLoading || isBulkUpdating}
                >
                  批量下架
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkAction('delete')}
                  disabled={bulkActionLoading || isBulkDeleting}
                >
                  批量刪除
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 商品列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>商品列表</CardTitle>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                共 {totalCount} 個商品
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暫無商品</h3>
              <p className="text-gray-500 mb-4">開始新增您的第一個商品吧！</p>
              <Button asChild>
                <Link to="/vendor/products/new">
                  <Plus className="h-4 w-4 mr-2" />
                  新增商品
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 表格標題 */}
              <div className="flex items-center space-x-4 py-2 border-b text-sm font-medium text-gray-500">
                <div className="w-8">
                  <input
                    type="checkbox"
                    checked={selectedProducts.length === products.length && products.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300"
                  />
                </div>
                <div className="flex-1">商品資訊</div>
                <div className="w-24">價格</div>
                <div className="w-24">庫存</div>
                <div className="w-24">狀態</div>
                <div className="w-20">操作</div>
              </div>

              {/* 商品項目 */}
              {products.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center space-x-4 py-4 border-b hover:bg-gray-50"
                >
                  <div className="w-8">
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={() => toggleSelection(product.id)}
                      className="rounded border-gray-300"
                    />
                  </div>
                  
                  <div className="flex-1 flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      {product.image_url ? (
                        <img
                          src={product.image_url}
                          alt={product.name}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <Package className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{product.name}</h3>
                      <p className="text-sm text-gray-500">{product.category}</p>
                      {product.description && (
                        <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                          {product.description}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="w-24 text-right">
                    <span className="font-medium">${product.price}</span>
                    <span className="text-sm text-gray-500">/{product.unit}</span>
                  </div>

                  <div className="w-24 text-right">
                    <div className="flex flex-col items-end">
                      <span className="font-medium">{product.stock}</span>
                      {getStockBadge(product.stock)}
                    </div>
                  </div>

                  <div className="w-24">
                    {getStatusBadge(product.status)}
                  </div>

                  <div className="w-20">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link to={`/vendor/products/${product.id}`}>
                            <Eye className="h-4 w-4 mr-2" />
                            查看
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link to={`/vendor/products/${product.id}/edit`}>
                            <Edit className="h-4 w-4 mr-2" />
                            編輯
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteProduct(product.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          刪除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 刪除確認對話框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>確認刪除商品</AlertDialogTitle>
            <AlertDialogDescription>
              此操作無法復原。確定要刪除這個商品嗎？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteProduct}
              className="bg-red-600 hover:bg-red-700"
            >
              確認刪除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 批量刪除確認對話框 */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>確認批量刪除</AlertDialogTitle>
            <AlertDialogDescription>
              此操作無法復原。確定要刪除選中的 {selectedProducts.length} 個商品嗎？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              確認刪除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default VendorProducts;
