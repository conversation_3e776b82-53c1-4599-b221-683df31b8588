import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useVendorAuth } from './useVendorAuth';
import type { VendorDashboardStats } from '@/types/database';

/**
 * 廠商儀表板數據 Hook
 * 獲取廠商的統計數據和關鍵指標
 */
export const useVendorDashboard = () => {
  const { vendor, isActiveVendor } = useVendorAuth();

  const {
    data: stats,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['vendor-dashboard', vendor?.id],
    queryFn: async (): Promise<VendorDashboardStats> => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      // 並行查詢所有統計數據
      const [
        productsResult,
        ordersResult,
        revenueResult,
        lowStockResult
      ] = await Promise.all([
        // 商品統計
        supabase
          .from('products')
          .select('id, status')
          .eq('vendor_id', vendor.id),

        // 訂單統計
        supabase
          .from('order_items')
          .select(`
            id,
            subtotal,
            created_at,
            orders!inner(status)
          `)
          .eq('vendor_id', vendor.id),

        // 今日和本月收益
        supabase
          .from('order_items')
          .select(`
            subtotal,
            created_at,
            orders!inner(status)
          `)
          .eq('vendor_id', vendor.id)
          .in('orders.status', ['COMPLETED']),

        // 低庫存商品
        supabase
          .from('products')
          .select('id, stock')
          .eq('vendor_id', vendor.id)
          .eq('status', 'ACTIVE')
          .lt('stock', 10) // 庫存少於 10 視為低庫存
      ]);

      // 處理錯誤
      if (productsResult.error) throw productsResult.error;
      if (ordersResult.error) throw ordersResult.error;
      if (revenueResult.error) throw revenueResult.error;
      if (lowStockResult.error) throw lowStockResult.error;

      // 計算商品統計
      const products = productsResult.data || [];
      const totalProducts = products.length;
      const activeProducts = products.filter(p => p.status === 'ACTIVE').length;

      // 計算訂單統計
      const orderItems = ordersResult.data || [];
      const totalOrders = new Set(orderItems.map(item => item.orders?.status)).size;
      const pendingOrders = orderItems.filter(item => 
        item.orders?.status === 'PENDING' || item.orders?.status === 'CONFIRMED'
      ).length;

      // 計算收益統計
      const completedOrderItems = revenueResult.data || [];
      const today = new Date().toISOString().split('T')[0];
      const thisMonth = new Date().toISOString().slice(0, 7);

      const todayRevenue = completedOrderItems
        .filter(item => item.created_at.startsWith(today))
        .reduce((sum, item) => sum + (item.subtotal || 0), 0);

      const monthlyRevenue = completedOrderItems
        .filter(item => item.created_at.startsWith(thisMonth))
        .reduce((sum, item) => sum + (item.subtotal || 0), 0);

      // 低庫存商品數量
      const lowStockProducts = lowStockResult.data?.length || 0;

      return {
        totalProducts,
        activeProducts,
        totalOrders,
        pendingOrders,
        todayRevenue,
        monthlyRevenue,
        lowStockProducts
      };
    },
    enabled: !!vendor?.id && isActiveVendor,
    staleTime: 2 * 60 * 1000, // 2 分鐘
    refetchInterval: 5 * 60 * 1000, // 每 5 分鐘自動刷新
  });

  return {
    stats,
    isLoading,
    error,
    refetch
  };
};

/**
 * 廠商最近訂單 Hook
 * 獲取廠商最近的訂單列表
 */
export const useVendorRecentOrders = (limit: number = 5) => {
  const { vendor, isActiveVendor } = useVendorAuth();

  return useQuery({
    queryKey: ['vendor-recent-orders', vendor?.id, limit],
    queryFn: async () => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      const { data, error } = await supabase
        .from('order_items')
        .select(`
          id,
          product_name,
          quantity,
          unit_price,
          subtotal,
          created_at,
          orders!inner(
            id,
            customer_name,
            customer_phone,
            status,
            created_at
          )
        `)
        .eq('vendor_id', vendor.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data;
    },
    enabled: !!vendor?.id && isActiveVendor,
    staleTime: 1 * 60 * 1000, // 1 分鐘
  });
};

/**
 * 廠商低庫存商品 Hook
 * 獲取庫存不足的商品列表
 */
export const useVendorLowStockProducts = (threshold: number = 10) => {
  const { vendor, isActiveVendor } = useVendorAuth();

  return useQuery({
    queryKey: ['vendor-low-stock', vendor?.id, threshold],
    queryFn: async () => {
      if (!vendor?.id) {
        throw new Error('廠商 ID 不存在');
      }

      const { data, error } = await supabase
        .from('products')
        .select('id, name, stock, unit, status')
        .eq('vendor_id', vendor.id)
        .eq('status', 'ACTIVE')
        .lt('stock', threshold)
        .order('stock', { ascending: true });

      if (error) throw error;
      return data;
    },
    enabled: !!vendor?.id && isActiveVendor,
    staleTime: 2 * 60 * 1000, // 2 分鐘
  });
};
