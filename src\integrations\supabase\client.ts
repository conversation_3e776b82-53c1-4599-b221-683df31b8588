// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jytufqhicuzyywalmjlh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp5dHVmcWhpY3V6eXl3YWxtamxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4NTU0NzEsImV4cCI6MjA2NDQzMTQ3MX0.VsncAa95LGgy6riupiqGXqrcuKBTTVKfzR7yKt_42So";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);