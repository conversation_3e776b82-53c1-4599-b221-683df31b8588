
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, MapPin, ShoppingCart, Eye, Heart, Package, ChevronLeft, ChevronRight, Plus, Minus } from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { toast } from 'sonner';

interface EnhancedProductCardProps {
  id: string;
  name: string;
  price: number;
  unit: string;
  images: string[];
  vendor: {
    name: string;
    location: string;
    verified?: boolean;
  };
  rating: number;
  reviewCount: number;
  isOrganic?: boolean;
  stock: number;
  minOrder?: number;
  discount?: number;
  isNew?: boolean;
  onQuickView?: () => void;
  onToggleFavorite?: () => void;
  isFavorite?: boolean;
}

const EnhancedProductCard: React.FC<EnhancedProductCardProps> = ({
  id,
  name,
  price,
  unit,
  images,
  vendor,
  rating,
  reviewCount,
  isOrganic = false,
  stock,
  minOrder = 1,
  discount,
  isNew = false,
  onQuickView,
  onToggleFavorite,
  isFavorite = false,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(minOrder);
  const [isHovered, setIsHovered] = useState(false);
  const { addItem } = useCart();

  const handlePrevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex(prev => prev === 0 ? images.length - 1 : prev - 1);
  };

  const handleNextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex(prev => prev === images.length - 1 ? 0 : prev + 1);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (stock === 0) {
      toast.error('商品庫存不足');
      return;
    }
    
    addItem({
      product_id: id,
      vendor_id: vendor.name, // 暫時使用 vendor name
      product_name: name,
      unit_price: discount ? price * (1 - discount / 100) : price,
      quantity,
      unit,
      image_url: images[0],
      vendor_name: vendor.name,
    });
  };

  const incrementQuantity = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (quantity < stock) {
      setQuantity(prev => prev + 1);
    }
  };

  const decrementQuantity = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (quantity > minOrder) {
      setQuantity(prev => prev - 1);
    }
  };

  const finalPrice = discount ? price * (1 - discount / 100) : price;
  const isInStock = stock > 0;

  return (
    <Card 
      className={`group hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:-translate-y-1 ${
        !isInStock ? 'opacity-75' : ''
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onQuickView}
    >
      <div className="relative">
        {/* Image Gallery */}
        <div className="relative h-48 overflow-hidden">
          <img 
            src={images[currentImageIndex] || '/placeholder.svg'} 
            alt={name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
          
          {/* Image Navigation */}
          {images.length > 1 && isHovered && (
            <>
              <button
                onClick={handlePrevImage}
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white rounded-full p-1 hover:bg-black/70 transition-colors"
                aria-label="上一張圖片"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              <button
                onClick={handleNextImage}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white rounded-full p-1 hover:bg-black/70 transition-colors"
                aria-label="下一張圖片"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
              
              {/* Image Indicators */}
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-1">
                {images.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </>
          )}
        </div>

        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {isNew && (
            <Badge className="bg-fresh-orange text-white text-xs">
              🆕 新品
            </Badge>
          )}
          {isOrganic && (
            <Badge className="bg-market-green-500 text-white text-xs">
              🌱 有機
            </Badge>
          )}
          {discount && (
            <Badge className="bg-red-500 text-white text-xs">
              -{discount}%
            </Badge>
          )}
          {!isInStock && (
            <Badge variant="destructive" className="text-xs">
              缺貨
            </Badge>
          )}
        </div>

        {/* Quick Actions */}
        <div className={`absolute top-3 right-3 flex flex-col gap-2 transition-opacity duration-200 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <Button
            size="sm"
            variant="secondary"
            className="w-8 h-8 p-0 bg-white/90 hover:bg-white"
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite?.();
            }}
            aria-label={isFavorite ? "移除收藏" : "加入收藏"}
          >
            <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
          </Button>
          <Button
            size="sm"
            variant="secondary"
            className="w-8 h-8 p-0 bg-white/90 hover:bg-white"
            onClick={(e) => {
              e.stopPropagation();
              onQuickView?.();
            }}
            aria-label="快速查看"
          >
            <Eye className="w-4 h-4 text-gray-600" />
          </Button>
        </div>

        {/* Price Tag */}
        <div className="absolute bottom-3 right-3">
          <div className="bg-white/95 backdrop-blur-sm rounded-lg px-3 py-1 shadow-sm">
            <div className="flex items-center space-x-2">
              {discount && (
                <span className="text-sm text-gray-500 line-through">
                  ${price}
                </span>
              )}
              <span className="text-lg font-bold text-market-green-600">
                ${finalPrice}
              </span>
              <span className="text-sm text-gray-600">/{unit}</span>
            </div>
          </div>
        </div>
      </div>

      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Product Name */}
          <h3 className="font-semibold text-lg text-gray-900 line-clamp-2 group-hover:text-market-green-600 transition-colors">
            {name}
          </h3>

          {/* Vendor Info */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-700">{vendor.name}</span>
              {vendor.verified && (
                <Badge variant="secondary" className="text-xs px-1 py-0">
                  ✓ 認證
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-1 text-gray-500">
              <MapPin className="w-3 h-3" />
              <span className="text-xs">{vendor.location}</span>
            </div>
          </div>

          {/* Rating & Stock */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium">{rating}</span>
              </div>
              <span className="text-sm text-gray-500">({reviewCount})</span>
            </div>
            <div className="flex items-center space-x-1 text-sm">
              <Package className="w-3 h-3 text-gray-400" />
              <span className={`${stock > 10 ? 'text-green-600' : stock > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                庫存 {stock}
              </span>
            </div>
          </div>

          {/* Quantity Selector & Add to Cart */}
          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">數量:</span>
              <div className="flex items-center border rounded-lg">
                <button
                  onClick={decrementQuantity}
                  disabled={quantity <= minOrder}
                  className="p-1 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="減少數量"
                >
                  <Minus className="w-3 h-3" />
                </button>
                <span className="px-3 py-1 text-sm font-medium">{quantity}</span>
                <button
                  onClick={incrementQuantity}
                  disabled={quantity >= stock}
                  className="p-1 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="增加數量"
                >
                  <Plus className="w-3 h-3" />
                </button>
              </div>
            </div>
            
            <Button 
              size="sm"
              className="bg-market-green-500 hover:bg-market-green-600 text-white"
              disabled={!isInStock}
              onClick={handleAddToCart}
              aria-label={`加入 ${quantity} ${unit} ${name} 到購物車`}
            >
              <ShoppingCart className="w-4 h-4 mr-1" />
              加入購物車
            </Button>
          </div>

          {minOrder > 1 && (
            <p className="text-xs text-gray-500">
              最少訂購 {minOrder} {unit}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedProductCard;
