
import React, { useState, useMemo } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/shared/Footer';
import CategoryFilter from '@/components/shared/CategoryFilter';
import ProductGrid from '@/components/shared/ProductGrid';
import ProductSearch from '@/components/shared/ProductSearch';
import ProductFilter, { FilterOptions } from '@/components/shared/ProductFilter';
import { useProducts } from '@/hooks/useProducts';
import ProductCard from '@/components/shared/ProductCard';
import { Skeleton } from '@/components/ui/skeleton';

const Products = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    categories: [],
    priceRange: { min: 0, max: 1000 },
    sortBy: 'name',
    inStockOnly: false,
    isOrganic: false
  });

  const { data: allProducts, isLoading } = useProducts();

  // 可用的分類列表
  const availableCategories = useMemo(() => {
    if (!allProducts) return [];
    const categories = [...new Set(allProducts.map(p => p.category))];
    return categories.filter(Boolean);
  }, [allProducts]);

  // 篩選和搜尋邏輯
  const filteredProducts = useMemo(() => {
    if (!allProducts) return [];

    let filtered = [...allProducts];

    // 搜尋篩選
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.vendor?.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
      );
    }

    // 分類篩選
    if (filters.categories.length > 0) {
      filtered = filtered.filter(product =>
        filters.categories.includes(product.category)
      );
    }

    // 庫存篩選
    if (filters.inStockOnly) {
      filtered = filtered.filter(product => product.stock > 0);
    }

    // 有機商品篩選 (假設有 isOrganic 欄位或從名稱判斷)
    if (filters.isOrganic) {
      filtered = filtered.filter(product =>
        product.name.includes('有機') ||
        product.description?.includes('有機')
      );
    }

    // 價格範圍篩選
    filtered = filtered.filter(product =>
      product.price >= filters.priceRange.min &&
      product.price <= filters.priceRange.max
    );

    // 排序
    switch (filters.sortBy) {
      case 'price_asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price_desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'rating':
        // 暫時隨機排序，之後可以根據實際評分排序
        filtered.sort(() => Math.random() - 0.5);
        break;
      case 'name':
      default:
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    return filtered;
  }, [allProducts, searchQuery, filters]);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">所有商品</h1>
          <p className="text-lg text-gray-600 mb-6">發現最新鮮的農產品</p>

          {/* 搜尋欄 */}
          <div className="mb-6">
            <ProductSearch
              onSearch={setSearchQuery}
              className="max-w-md"
            />
          </div>

          {/* 篩選器 */}
          <div className="mb-6">
            <ProductFilter
              filters={filters}
              onFiltersChange={setFilters}
              availableCategories={availableCategories}
            />
          </div>
        </div>

        {/* 結果統計 */}
        <div className="mb-6 flex items-center justify-between">
          <p className="text-gray-600">
            找到 <span className="font-medium text-green-600">{filteredProducts.length}</span> 項商品
            {searchQuery && (
              <span className="ml-2">
                搜尋: "<span className="font-medium">{searchQuery}</span>"
              </span>
            )}
          </p>
        </div>

        {/* 商品列表 */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="space-y-4">
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <ProductCard
                key={product.id}
                id={product.id}
                name={product.name}
                price={product.price}
                unit={product.unit}
                image={product.image_url || '/placeholder.svg'}
                vendor={product.vendor?.name || '未知供應商'}
                location={product.vendor?.location || '未知地點'}
                rating={4.5}
                reviewCount={Math.floor(Math.random() * 200) + 10}
                inStock={product.stock > 0}
                stock={product.stock}
                minOrder={product.unit === 'KG' ? 5 : product.unit === 'G' ? 500 : 1}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">沒有找到符合條件的商品</p>
            <p className="text-gray-500 mt-2">請嘗試調整搜尋條件或篩選器</p>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
};

export default Products;
