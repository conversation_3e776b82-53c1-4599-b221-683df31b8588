import React, { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface ProductSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
  className?: string;
}

const ProductSearch: React.FC<ProductSearchProps> = ({
  onSearch,
  placeholder = "搜尋商品名稱、廠商...",
  className = ""
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // 使用 debounce 來避免過於頻繁的搜尋
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, onSearch]);

  const handleClear = () => {
    setSearchQuery('');
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-10"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>
      
      {searchQuery && (
        <div className="absolute top-full left-0 right-0 mt-1 text-sm text-gray-600 bg-white p-2 rounded-md border shadow-sm">
          搜尋: "{searchQuery}"
        </div>
      )}
    </div>
  );
};

export default ProductSearch;
