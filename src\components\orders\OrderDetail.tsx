import React from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, CheckCircle, Clock, Package, Truck, XCircle, Phone, MapPin, Calendar } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { useOrders } from '@/hooks/useOrders';

const OrderDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { useOrder, updateOrderStatus, cancelOrder, isUpdatingStatus, isCancellingOrder } = useOrders();
  
  const { data: order, isLoading, error } = useOrder(id!);
  const orderCreated = location.state?.orderCreated;

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: '待確認',
          color: 'bg-yellow-100 text-yellow-800',
          icon: Clock,
          description: '訂單已提交，等待廠商確認'
        };
      case 'CONFIRMED':
        return {
          label: '已確認',
          color: 'bg-blue-100 text-blue-800',
          icon: CheckCircle,
          description: '訂單已確認，廠商正在準備商品'
        };
      case 'PREPARING':
        return {
          label: '準備中',
          color: 'bg-purple-100 text-purple-800',
          icon: Package,
          description: '商品準備中，請稍候'
        };
      case 'READY':
        return {
          label: '可取貨',
          color: 'bg-green-100 text-green-800',
          icon: Truck,
          description: '商品已準備完成，可前往取貨'
        };
      case 'COMPLETED':
        return {
          label: '已完成',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle,
          description: '訂單已完成'
        };
      case 'CANCELLED':
        return {
          label: '已取消',
          color: 'bg-red-100 text-red-800',
          icon: XCircle,
          description: '訂單已取消'
        };
      default:
        return {
          label: status,
          color: 'bg-gray-100 text-gray-800',
          icon: Clock,
          description: ''
        };
    }
  };

  const handleStatusUpdate = (newStatus: string) => {
    if (id) {
      updateOrderStatus({ orderId: id, status: newStatus as any });
    }
  };

  const handleCancelOrder = () => {
    if (id && window.confirm('確定要取消這個訂單嗎？取消後將恢復商品庫存。')) {
      cancelOrder(id);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">載入訂單資訊中...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <XCircle className="w-16 h-16 mx-auto text-red-400 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">找不到訂單</h2>
            <p className="text-gray-600 mb-6">訂單可能不存在或已被刪除</p>
            <Button onClick={() => navigate('/')} className="w-full">
              返回首頁
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const statusInfo = getStatusInfo(order.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 返回按鈕 */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <Button
            variant="ghost"
            onClick={() => navigate(-1)}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回</span>
          </Button>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* 訂單創建成功提示 */}
          {orderCreated && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                訂單建立成功！我們已收到您的訂單，廠商將盡快確認。
              </AlertDescription>
            </Alert>
          )}

          {/* 訂單標題和狀態 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-gray-900">
                訂單 #{order.id.slice(-8)}
              </h1>
              <Badge className={statusInfo.color}>
                <StatusIcon className="w-4 h-4 mr-1" />
                {statusInfo.label}
              </Badge>
            </div>
            <p className="text-gray-600">{statusInfo.description}</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左側：訂單詳情 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 訂單商品 */}
              <Card>
                <CardHeader>
                  <CardTitle>訂單商品</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.order_items.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Package className="w-6 h-6 text-gray-400" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{item.product_name}</h4>
                          <p className="text-sm text-gray-600">
                            {item.vendor?.name} | {item.vendor?.location}
                          </p>
                          <p className="text-sm text-gray-500">
                            數量: {item.quantity} | 單價: NT$ {item.unit_price}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">
                            NT$ {item.subtotal.toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator className="my-4" />

                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">總計</span>
                    <span className="text-xl font-bold text-green-600">
                      NT$ {order.total_amount.toLocaleString()}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* 訂單操作 */}
              {order.status === 'PENDING' && (
                <Card>
                  <CardHeader>
                    <CardTitle>訂單操作</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex space-x-3">
                      <Button
                        variant="outline"
                        onClick={() => handleStatusUpdate('CONFIRMED')}
                        disabled={isUpdatingStatus}
                      >
                        確認訂單
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleCancelOrder}
                        disabled={isCancellingOrder}
                      >
                        取消訂單
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* 右側：客戶資訊和訂單資訊 */}
            <div className="space-y-6">
              {/* 客戶資訊 */}
              <Card>
                <CardHeader>
                  <CardTitle>客戶資訊</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="font-medium">{order.customer_name}</p>
                      <p className="text-sm text-gray-600">{order.customer_phone}</p>
                    </div>
                  </div>
                  
                  {order.license_plate && (
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600">車牌號碼</p>
                        <p className="font-medium">{order.license_plate}</p>
                      </div>
                    </div>
                  )}

                  {order.notes && (
                    <div>
                      <p className="text-sm text-gray-600 mb-1">備註</p>
                      <p className="text-sm bg-gray-50 p-2 rounded">{order.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 訂單資訊 */}
              <Card>
                <CardHeader>
                  <CardTitle>訂單資訊</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">建立時間</p>
                      <p className="font-medium">
                        {new Date(order.created_at).toLocaleString('zh-TW')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">最後更新</p>
                      <p className="font-medium">
                        {new Date(order.updated_at).toLocaleString('zh-TW')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 取貨資訊 */}
              {order.status === 'READY' && (
                <Card className="border-green-200 bg-green-50">
                  <CardHeader>
                    <CardTitle className="text-green-800">取貨資訊</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-green-700 text-sm">
                      您的訂單已準備完成，請前往各廠商攤位取貨。
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;
