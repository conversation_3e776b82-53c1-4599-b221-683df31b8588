
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface OrderFormData {
  customer_name: string;
  customer_phone: string;
  license_plate?: string;
  notes?: string;
}

interface OrderState {
  currentOrder: OrderFormData | null;
  orderHistory: string[]; // order IDs
  setOrderForm: (data: Partial<OrderFormData>) => void;
  clearOrderForm: () => void;
  addToHistory: (orderId: string) => void;
  clearHistory: () => void;
}

export const useOrderStore = create<OrderState>()(
  persist(
    (set) => ({
      currentOrder: null,
      orderHistory: [],
      setOrderForm: (data) => set((state) => ({
        currentOrder: state.currentOrder 
          ? { ...state.currentOrder, ...data }
          : data as OrderFormData
      })),
      clearOrderForm: () => set({ currentOrder: null }),
      addToHistory: (orderId) => set((state) => ({
        orderHistory: [orderId, ...state.orderHistory.slice(0, 9)] // Keep last 10
      })),
      clearHistory: () => set({ orderHistory: [] }),
    }),
    {
      name: 'order-store',
    }
  )
);
