
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { Order, OrderItem } from '@/types/database';

interface CreateOrderData {
  customer_name: string;
  customer_phone: string;
  license_plate?: string;
  notes?: string;
  items: Array<{
    product_id: string;
    vendor_id: string;
    product_name: string;
    quantity: number;
    unit_price: number;
  }>;
}

export const useOrders = () => {
  const queryClient = useQueryClient();

  const createOrderMutation = useMutation({
    mutationFn: async (orderData: CreateOrderData) => {
      const total_amount = orderData.items.reduce(
        (sum, item) => sum + (item.quantity * item.unit_price), 0
      );

      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_name: orderData.customer_name,
          customer_phone: orderData.customer_phone,
          license_plate: orderData.license_plate,
          notes: orderData.notes,
          total_amount
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        ...item,
        subtotal: item.quantity * item.unit_price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Update product stock
      for (const item of orderData.items) {
        const { error: stockError } = await supabase.rpc('decrement_stock', {
          product_id: item.product_id,
          quantity: item.quantity
        });

        if (stockError) {
          console.error('Stock update error:', stockError);
          // Don't throw here to avoid partial order creation
        }
      }

      return order;
    },
    onSuccess: () => {
      toast.success('訂單創建成功！');
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
    onError: (error: any) => {
      console.error('Create order error:', error);
      toast.error(error.message || '訂單創建失敗');
    }
  });

  const updateOrderStatusMutation = useMutation({
    mutationFn: async ({ orderId, status }: { orderId: string; status: Order['status'] }) => {
      const { error } = await supabase
        .from('orders')
        .update({ status })
        .eq('id', orderId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('訂單狀態已更新');
      queryClient.invalidateQueries({ queryKey: ['orders'] });
    },
    onError: (error: any) => {
      console.error('Update order status error:', error);
      toast.error(error.message || '更新訂單狀態失敗');
    }
  });

  // 獲取所有訂單（管理員用）
  const useAllOrders = (filters?: {
    status?: string;
    customer_phone?: string;
    date_from?: string;
    date_to?: string;
  }) => {
    return useQuery({
      queryKey: ['orders', 'all', filters],
      queryFn: async () => {
        let query = supabase
          .from('orders')
          .select(`
            *,
            order_items (
              *,
              vendor:vendors (
                name,
                location
              )
            )
          `)
          .order('created_at', { ascending: false });

        // 應用篩選條件
        if (filters?.status) {
          query = query.eq('status', filters.status);
        }

        if (filters?.customer_phone) {
          query = query.ilike('customer_phone', `%${filters.customer_phone}%`);
        }

        if (filters?.date_from) {
          query = query.gte('created_at', filters.date_from);
        }

        if (filters?.date_to) {
          query = query.lte('created_at', filters.date_to);
        }

        const { data, error } = await query;

        if (error) throw error;
        return data as (Order & {
          order_items: (OrderItem & {
            vendor: { name: string; location: string }
          })[]
        })[];
      }
    });
  };

  // 獲取單一訂單
  const useOrder = (orderId: string) => {
    return useQuery({
      queryKey: ['orders', orderId],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('orders')
          .select(`
            *,
            order_items (
              *,
              vendor:vendors (
                name,
                location,
                phone
              )
            )
          `)
          .eq('id', orderId)
          .single();

        if (error) throw error;
        return data as Order & {
          order_items: (OrderItem & {
            vendor: { name: string; location: string; phone: string }
          })[]
        };
      },
      enabled: !!orderId
    });
  };

  const useVendorOrders = (vendorId?: string) => {
    return useQuery({
      queryKey: ['orders', 'vendor', vendorId],
      queryFn: async () => {
        if (!vendorId) return [];

        const { data, error } = await supabase
          .from('orders')
          .select(`
            *,
            order_items!inner(
              *,
              product:products(name, image_url)
            )
          `)
          .eq('order_items.vendor_id', vendorId)
          .order('created_at', { ascending: false });

        if (error) throw error;
        return data as (Order & { order_items: (OrderItem & { product: { name: string; image_url?: string } })[] })[];
      },
      enabled: !!vendorId
    });
  };

  // 取消訂單
  const cancelOrderMutation = useMutation({
    mutationFn: async (orderId: string) => {
      // 獲取訂單詳情以恢復庫存
      const { data: order, error: fetchError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (*)
        `)
        .eq('id', orderId)
        .single();

      if (fetchError) throw fetchError;

      // 更新訂單狀態為取消
      const { error: updateError } = await supabase
        .from('orders')
        .update({
          status: 'CANCELLED',
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (updateError) throw updateError;

      // 恢復商品庫存
      for (const item of order.order_items) {
        const { error: stockError } = await supabase.rpc('increment_stock', {
          product_id: item.product_id,
          quantity: item.quantity
        });

        if (stockError) {
          console.error('Stock restore error:', stockError);
        }
      }

      return order;
    },
    onSuccess: () => {
      toast.success('訂單已取消，庫存已恢復！');
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
    onError: (error: any) => {
      console.error('Cancel order error:', error);
      toast.error(error.message || '取消訂單失敗');
    }
  });

  return {
    createOrder: createOrderMutation.mutate,
    updateOrderStatus: updateOrderStatusMutation.mutate,
    cancelOrder: cancelOrderMutation.mutate,
    useAllOrders,
    useOrder,
    useVendorOrders,
    isCreatingOrder: createOrderMutation.isPending,
    isUpdatingStatus: updateOrderStatusMutation.isPending,
    isCancellingOrder: cancelOrderMutation.isPending
  };
};
