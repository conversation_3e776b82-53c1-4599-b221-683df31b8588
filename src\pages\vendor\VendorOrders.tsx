import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  ShoppingCart,
  Calendar,
  Phone,
  User
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useVendorOrders } from '@/hooks/useVendorOrders';
import { useVendorOrderStore } from '@/stores/vendorStore';
import { cn } from '@/lib/utils';
import type { OrderStatus } from '@/types/database';

/**
 * 廠商訂單管理頁面
 */
const VendorOrders: React.FC = () => {
  const {
    filters,
    setFilter,
    clearFilters,
    selectedOrders,
    toggleSelection,
    setSelectedOrders,
    bulkActionLoading,
    setBulkActionLoading
  } = useVendorOrderStore();

  const {
    orders,
    totalCount,
    isLoading,
    updateOrderStatus,
    bulkUpdateOrderStatus,
    isUpdatingStatus,
    isBulkUpdating
  } = useVendorOrders(filters);

  const [bulkStatusDialogOpen, setBulkStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>('CONFIRMED');

  // 處理搜尋
  const handleSearch = (value: string) => {
    setFilter('search', value || undefined);
  };

  // 處理狀態篩選
  const handleStatusFilter = (value: string) => {
    setFilter('status', value === 'all' ? undefined : value as OrderStatus);
  };

  // 處理日期篩選
  const handleDateFromFilter = (value: string) => {
    setFilter('dateFrom', value || undefined);
  };

  const handleDateToFilter = (value: string) => {
    setFilter('dateTo', value || undefined);
  };

  // 處理分頁
  const handlePageChange = (page: number) => {
    setFilter('page', page);
  };

  // 處理單個訂單狀態更新
  const handleUpdateOrderStatus = (orderId: string, status: OrderStatus) => {
    updateOrderStatus({ orderId, status });
  };

  // 處理批量狀態更新
  const handleBulkStatusUpdate = () => {
    if (selectedOrders.length === 0) return;
    setBulkStatusDialogOpen(true);
  };

  const confirmBulkStatusUpdate = () => {
    bulkUpdateOrderStatus({ 
      orderIds: selectedOrders.map(order => order.orders.id), 
      status: selectedStatus 
    });
    setBulkStatusDialogOpen(false);
    setSelectedOrders([]);
  };

  // 全選/取消全選
  const handleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(orders);
    }
  };

  const getStatusBadge = (status: OrderStatus) => {
    const statusConfig = {
      'PENDING': { label: '待確認', color: 'bg-yellow-100 text-yellow-800' },
      'CONFIRMED': { label: '已確認', color: 'bg-blue-100 text-blue-800' },
      'PREPARING': { label: '準備中', color: 'bg-orange-100 text-orange-800' },
      'READY': { label: '待取貨', color: 'bg-purple-100 text-purple-800' },
      'COMPLETED': { label: '已完成', color: 'bg-green-100 text-green-800' },
      'CANCELLED': { label: '已取消', color: 'bg-red-100 text-red-800' },
    };

    const config = statusConfig[status] || statusConfig['PENDING'];
    
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 按訂單分組
  const groupedOrders = orders.reduce((acc, item) => {
    const orderId = item.orders.id;
    if (!acc[orderId]) {
      acc[orderId] = {
        order: item.orders,
        items: []
      };
    }
    acc[orderId].items.push(item);
    return acc;
  }, {} as Record<string, { order: any; items: any[] }>);

  const orderGroups = Object.values(groupedOrders);

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">訂單管理</h1>
          <p className="text-gray-600 mt-1">
            管理您的訂單和處理狀態
          </p>
        </div>
      </div>

      {/* 篩選和搜尋 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">篩選和搜尋</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* 搜尋框 */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="搜尋客戶姓名、電話或商品..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 狀態篩選 */}
            <Select value={filters.status || 'all'} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部狀態</SelectItem>
                <SelectItem value="PENDING">待確認</SelectItem>
                <SelectItem value="CONFIRMED">已確認</SelectItem>
                <SelectItem value="PREPARING">準備中</SelectItem>
                <SelectItem value="READY">待取貨</SelectItem>
                <SelectItem value="COMPLETED">已完成</SelectItem>
                <SelectItem value="CANCELLED">已取消</SelectItem>
              </SelectContent>
            </Select>

            {/* 日期篩選 */}
            <Input
              type="date"
              value={filters.dateFrom || ''}
              onChange={(e) => handleDateFromFilter(e.target.value)}
              className="w-40"
            />
            <Input
              type="date"
              value={filters.dateTo || ''}
              onChange={(e) => handleDateToFilter(e.target.value)}
              className="w-40"
            />

            {/* 清除篩選 */}
            <Button variant="outline" onClick={clearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              清除篩選
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 批量操作 */}
      {selectedOrders.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                已選擇 {selectedOrders.length} 個訂單
              </span>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleBulkStatusUpdate}
                  disabled={bulkActionLoading || isBulkUpdating}
                >
                  批量更新狀態
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 訂單列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>訂單列表</CardTitle>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                共 {totalCount} 個訂單項目
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-24 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : orderGroups.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暫無訂單</h3>
              <p className="text-gray-500">等待客戶下單...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {orderGroups.map(({ order, items }) => (
                <div
                  key={order.id}
                  className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  {/* 訂單標題 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedOrders.some(o => o.orders.id === order.id)}
                        onChange={() => toggleSelection(items[0])}
                        className="rounded border-gray-300"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">
                          訂單 #{order.id.slice(-8)}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(order.created_at)}
                          </span>
                          <span className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            {order.customer_name}
                          </span>
                          <span className="flex items-center">
                            <Phone className="h-4 w-4 mr-1" />
                            {order.customer_phone}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(order.status)}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link to={`/vendor/orders/${order.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看詳情
                            </Link>
                          </DropdownMenuItem>
                          {order.status === 'PENDING' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateOrderStatus(order.id, 'CONFIRMED')}
                            >
                              確認訂單
                            </DropdownMenuItem>
                          )}
                          {order.status === 'CONFIRMED' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateOrderStatus(order.id, 'PREPARING')}
                            >
                              開始準備
                            </DropdownMenuItem>
                          )}
                          {order.status === 'PREPARING' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateOrderStatus(order.id, 'READY')}
                            >
                              準備完成
                            </DropdownMenuItem>
                          )}
                          {order.status === 'READY' && (
                            <DropdownMenuItem
                              onClick={() => handleUpdateOrderStatus(order.id, 'COMPLETED')}
                            >
                              完成訂單
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  {/* 訂單項目 */}
                  <div className="space-y-2">
                    {items.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between py-2 px-4 bg-gray-50 rounded"
                      >
                        <div className="flex-1">
                          <span className="font-medium">{item.product_name}</span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>數量: {item.quantity}</span>
                          <span>單價: ${item.unit_price}</span>
                          <span className="font-medium">小計: ${item.subtotal}</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 訂單備註 */}
                  {order.notes && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-sm text-yellow-800">
                        <strong>備註：</strong>{order.notes}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 批量狀態更新對話框 */}
      <AlertDialog open={bulkStatusDialogOpen} onOpenChange={setBulkStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>批量更新訂單狀態</AlertDialogTitle>
            <AlertDialogDescription>
              選擇要更新的狀態，將會應用到選中的 {selectedOrders.length} 個訂單。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as OrderStatus)}>
              <SelectTrigger>
                <SelectValue placeholder="選擇狀態" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CONFIRMED">已確認</SelectItem>
                <SelectItem value="PREPARING">準備中</SelectItem>
                <SelectItem value="READY">待取貨</SelectItem>
                <SelectItem value="COMPLETED">已完成</SelectItem>
                <SelectItem value="CANCELLED">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmBulkStatusUpdate}>
              確認更新
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default VendorOrders;
