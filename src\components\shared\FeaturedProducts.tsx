
import React, { useState } from 'react';
import { useProducts } from '@/hooks/useProducts';
import ProductCard from './ProductCard';
import CategoryFilter from './CategoryFilter';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const FeaturedProducts = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>();
  const { data: products, isLoading, error } = useProducts();
  const navigate = useNavigate();

  // 根據選擇的分類篩選產品
  const filteredProducts = selectedCategory 
    ? products?.filter(product => product.category === selectedCategory)
    : products;

  // 限制顯示數量為 6 個
  const displayProducts = filteredProducts?.slice(0, 6) || [];

  if (error) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">精選商品</h2>
            <p className="text-lg text-gray-600">嚴選優質供應商，為您提供最新鮮的農產品</p>
          </div>
          <div className="text-center py-12">
            <p className="text-red-600 text-lg">載入產品時發生錯誤</p>
            <p className="text-gray-500 mt-2">請稍後再試</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">精選商品</h2>
          <p className="text-lg text-gray-600">嚴選優質供應商，為您提供最新鮮的農產品</p>
        </div>

        <div className="mb-8">
          <CategoryFilter
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
          />
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="space-y-4">
                <Skeleton className="h-48 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        ) : displayProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {displayProducts.map((product) => (
              <ProductCard 
                key={product.id}
                id={product.id}
                name={product.name}
                price={product.price}
                unit={product.unit}
                image={product.image_url || '/placeholder.svg'}
                vendor={product.vendor?.name || '未知供應商'}
                location={product.vendor?.location || '未知地點'}
                rating={4.5}
                reviewCount={Math.floor(Math.random() * 200) + 10}
                inStock={product.stock > 0}
                minOrder={product.unit === 'KG' ? 5 : product.unit === 'G' ? 500 : 1}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600 text-lg">
              {selectedCategory ? `${selectedCategory} 分類中暫無商品` : '目前沒有可用的產品'}
            </p>
            <p className="text-gray-500 mt-2">請稍後再查看</p>
          </div>
        )}

        <div className="text-center mt-12">
          <Button 
            className="bg-market-green-500 hover:bg-market-green-600 text-white px-8 py-3 rounded-xl font-medium transition-colors"
            onClick={() => navigate('/products')}
          >
            查看更多商品
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
