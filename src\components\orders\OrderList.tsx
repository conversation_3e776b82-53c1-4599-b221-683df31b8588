import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Search, Filter, Calendar, Phone, Package, Eye, Clock, CheckCircle, XCircle, Truck } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

import { useOrders } from '@/hooks/useOrders';

const OrderList = () => {
  const [filters, setFilters] = useState({
    status: '',
    customer_phone: '',
    date_from: '',
    date_to: ''
  });

  const { useAllOrders } = useOrders();
  const { data: orders, isLoading, error } = useAllOrders(filters);

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: '待確認',
          color: 'bg-yellow-100 text-yellow-800',
          icon: Clock
        };
      case 'CONFIRMED':
        return {
          label: '已確認',
          color: 'bg-blue-100 text-blue-800',
          icon: CheckCircle
        };
      case 'PREPARING':
        return {
          label: '準備中',
          color: 'bg-purple-100 text-purple-800',
          icon: Package
        };
      case 'READY':
        return {
          label: '可取貨',
          color: 'bg-green-100 text-green-800',
          icon: Truck
        };
      case 'COMPLETED':
        return {
          label: '已完成',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle
        };
      case 'CANCELLED':
        return {
          label: '已取消',
          color: 'bg-red-100 text-red-800',
          icon: XCircle
        };
      default:
        return {
          label: status,
          color: 'bg-gray-100 text-gray-800',
          icon: Clock
        };
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      customer_phone: '',
      date_from: '',
      date_to: ''
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 text-lg">載入訂單時發生錯誤</p>
        <p className="text-gray-500 mt-2">請稍後再試</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 篩選器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>篩選訂單</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 狀態篩選 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                訂單狀態
              </label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="所有狀態" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">所有狀態</SelectItem>
                  <SelectItem value="PENDING">待確認</SelectItem>
                  <SelectItem value="CONFIRMED">已確認</SelectItem>
                  <SelectItem value="PREPARING">準備中</SelectItem>
                  <SelectItem value="READY">可取貨</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="CANCELLED">已取消</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 電話搜尋 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                客戶電話
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜尋電話號碼"
                  value={filters.customer_phone}
                  onChange={(e) => handleFilterChange('customer_phone', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 開始日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                開始日期
              </label>
              <Input
                type="date"
                value={filters.date_from}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
              />
            </div>

            {/* 結束日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                結束日期
              </label>
              <Input
                type="date"
                value={filters.date_to}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
              />
            </div>
          </div>

          <div className="mt-4 flex space-x-2">
            <Button variant="outline" onClick={clearFilters}>
              清除篩選
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 訂單列表 */}
      <div className="space-y-4">
        {orders && orders.length > 0 ? (
          orders.map((order) => {
            const statusInfo = getStatusInfo(order.status);
            const StatusIcon = statusInfo.icon;

            return (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-2">
                        <h3 className="font-semibold text-lg">
                          訂單 #{order.id.slice(-8)}
                        </h3>
                        <Badge className={statusInfo.color}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusInfo.label}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <Phone className="w-4 h-4" />
                          <span>{order.customer_name}</span>
                          <span>({order.customer_phone})</span>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(order.created_at).toLocaleDateString('zh-TW')}</span>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Package className="w-4 h-4" />
                          <span>{order.order_items.length} 項商品</span>
                        </div>
                      </div>

                      <div className="mt-2">
                        <span className="text-lg font-semibold text-green-600">
                          NT$ {order.total_amount.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Link to={`/orders/${order.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          查看詳情
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        ) : (
          <div className="text-center py-12">
            <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">沒有找到訂單</h3>
            <p className="text-gray-600">
              {Object.values(filters).some(f => f) ? '請嘗試調整篩選條件' : '目前還沒有任何訂單'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderList;
