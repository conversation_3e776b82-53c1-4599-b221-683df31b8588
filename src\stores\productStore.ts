
import { create } from 'zustand';

interface ProductFilters {
  category?: string;
  vendor?: string;
  search?: string;
  priceRange?: [number, number];
  sortBy?: 'name' | 'price' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}

interface ProductState {
  filters: ProductFilters;
  viewMode: 'grid' | 'list';
  setFilter: (key: keyof ProductFilters, value: any) => void;
  clearFilters: () => void;
  setViewMode: (mode: 'grid' | 'list') => void;
}

export const useProductStore = create<ProductState>((set) => ({
  filters: {
    sortBy: 'created_at',
    sortOrder: 'desc',
  },
  viewMode: 'grid',
  setFilter: (key, value) => set((state) => ({
    filters: { ...state.filters, [key]: value }
  })),
  clearFilters: () => set({
    filters: {
      sortBy: 'created_at',
      sortOrder: 'desc',
    }
  }),
  setViewMode: (mode) => set({ viewMode: mode }),
}));
